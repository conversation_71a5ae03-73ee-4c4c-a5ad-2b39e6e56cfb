{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/core/base/GameConst.ts"], "names": ["_GameConst", "view", "ColliderDraw", "ActionFrameTime", "designWidth", "designHeight", "offsetWidth", "VIEWPORT_TOP", "VIEWPORT_LOAD_POS", "BATTLE_VIEW_TOP", "BATTLE_VIEW_BOTTOM", "BATTLE_VIEW_LEFT", "ViewBattleWidth", "BATTLE_VIEW_RIGHT", "ViewHeight", "getVisibleSize", "height", "ViewWidth", "width", "ViewTop", "ViewBottom", "isPointInBattleView", "position", "x", "y", "isRectInBattleView", "rect", "GameConst"], "mappings": ";;;wEAGMA,U;;;;;;;AAFGC,MAAAA,I,OAAAA,I;;;;;;;;;AAEHD,MAAAA,U,GAAN,MAAMA,UAAN,CAAiB;AAAA;AAAA,eAEJE,YAFI,GAEoB,KAFpB;AAAA,eAGJC,eAHI,GAGsB,MAHtB;AAAA,eAKbC,WALa,GAKS,GALT;AAKa;AALb,eAMbC,YANa,GAMU,IANV;AAMe;AANf,eAObC,WAPa,GAOS,GAPT;AAOa;AAPb,eASJC,YATI,GASmB,CAAC,OAAO,GAAR,IAAe,CATlC;AASqC;AATrC,eAUJC,iBAVI,GAUwB,OAAO,OAAO,CAVtC;AAUyC;AAVzC,eAYJC,eAZI,GAYsB,KAAKF,YAZ3B;AAAA,eAaJG,kBAbI,GAayB,CAAC,KAAKH,YAb/B;AAAA,eAcJI,gBAdI,GAcuB,KAAKC,eAAL,GAAuB,CAAC,CAd/C;AAAA,eAeJC,iBAfI,GAewB,KAAKD,eAAL,GAAuB,CAf/C;AAAA;;AAiBC,YAAVE,UAAU,GAAG;AACb,iBAAOb,IAAI,CAACc,cAAL,GAAsBC,MAA7B;AACH;;AAEY,YAATC,SAAS,GAAG;AACZ,iBAAOhB,IAAI,CAACc,cAAL,GAAsBG,KAA7B;AACH;;AAEU,YAAPC,OAAO,GAAG;AACV,iBAAOlB,IAAI,CAACc,cAAL,GAAsBC,MAAtB,GAA+B,CAAtC;AACH;;AAEa,YAAVI,UAAU,GAAG;AACb,iBAAOnB,IAAI,CAACc,cAAL,GAAsBC,MAAtB,GAA+B,CAAC,CAAvC;AACH;;AAEkB,YAAfJ,eAAe,GAAG;AAClB,cAAIM,KAAK,GAAGjB,IAAI,CAACc,cAAL,GAAsBG,KAAlC;AACA,iBAAOA,KAAK,GAAG,KAAKZ,WAApB;AACH;;AAEMe,QAAAA,mBAAmB,CAACC,QAAD,EAA0B;AAChD,iBAAOA,QAAQ,CAACC,CAAT,GAAa,KAAKZ,gBAAlB,IAAsCW,QAAQ,CAACC,CAAT,GAAa,KAAKV,iBAAxD,IACHS,QAAQ,CAACE,CAAT,GAAa,KAAKd,kBADf,IACqCY,QAAQ,CAACE,CAAT,GAAa,KAAKf,eAD9D;AAEH;;AAEMgB,QAAAA,kBAAkB,CAACC,IAAD,EAAsB;AAC3C,iBAAOA,IAAI,CAACH,CAAL,GAAS,KAAKZ,gBAAd,IAAkCe,IAAI,CAACH,CAAL,GAASG,IAAI,CAACR,KAAd,GAAsB,KAAKL,iBAA7D,IACHa,IAAI,CAACF,CAAL,GAAS,KAAKd,kBADX,IACiCgB,IAAI,CAACF,CAAL,GAASE,IAAI,CAACV,MAAd,GAAuB,KAAKP,eADpE;AAEH;;AA9CY,O;;2BAkDJkB,S,GAAY,IAAI3B,UAAJ,E", "sourcesContent": ["\r\nimport { view, Vec3, Rect } from \"cc\";\r\n\r\nclass _GameConst {\r\n\r\n    readonly ColliderDraw: boolean = false;\r\n    readonly ActionFrameTime: number = 0.0333;\r\n\r\n    designWidth: number = 750 // 设计分辨率\r\n    designHeight: number = 1334 // 设计分辨率\r\n    offsetWidth: number = 200 // 宽屏的时候，宽度最高多显示200像素\r\n\r\n    readonly VIEWPORT_TOP: number = (1334 + 120) / 2; // 视口顶部位置, 往上增加120偏移, 这个用来激活event\r\n    readonly VIEWPORT_LOAD_POS: number = 1334 + 1334 / 2; // 视口加载位置, 往上增加1334/2偏移\r\n\r\n    readonly BATTLE_VIEW_TOP: number = this.VIEWPORT_TOP;\r\n    readonly BATTLE_VIEW_BOTTOM: number = -this.VIEWPORT_TOP;\r\n    readonly BATTLE_VIEW_LEFT: number = this.ViewBattleWidth / -2;\r\n    readonly BATTLE_VIEW_RIGHT: number = this.ViewBattleWidth / 2;\r\n\r\n    get ViewHeight() {\r\n        return view.getVisibleSize().height\r\n    }\r\n\r\n    get ViewWidth() {\r\n        return view.getVisibleSize().width;\r\n    }\r\n\r\n    get ViewTop() {\r\n        return view.getVisibleSize().height / 2;\r\n    }\r\n\r\n    get ViewBottom() {\r\n        return view.getVisibleSize().height / -2;\r\n    }\r\n\r\n    get ViewBattleWidth() {\r\n        let width = view.getVisibleSize().width;\r\n        return width + this.offsetWidth;\r\n    }\r\n\r\n    public isPointInBattleView(position: Vec3): boolean {\r\n        return position.x > this.BATTLE_VIEW_LEFT && position.x < this.BATTLE_VIEW_RIGHT &&\r\n            position.y > this.BATTLE_VIEW_BOTTOM && position.y < this.BATTLE_VIEW_TOP;\r\n    }\r\n\r\n    public isRectInBattleView(rect: Rect): boolean {\r\n        return rect.x > this.BATTLE_VIEW_LEFT && rect.x + rect.width < this.BATTLE_VIEW_RIGHT &&\r\n            rect.y > this.BATTLE_VIEW_BOTTOM && rect.y + rect.height < this.BATTLE_VIEW_TOP;\r\n    }\r\n\r\n}\r\n\r\nexport const GameConst = new _GameConst();"]}