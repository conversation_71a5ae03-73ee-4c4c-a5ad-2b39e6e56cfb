System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, GameConst, _dec, _class, _crd, ccclass, eEntityTag, Entity;

  function _reportPossibleCrUseOfBaseComp(extras) {
    _reporterNs.report("BaseComp", "./BaseComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFCollider(extras) {
    _reporterNs.report("FCollider", "../../collider-system/FCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "db://assets/scripts/core/base/GameConst", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
    }, function (_unresolved_2) {
      GameConst = _unresolved_2.GameConst;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "5d689opT5VP6qES8dSpWVRi", "Entity", undefined);

      __checkObsolete__(['_decorator', 'Component']);

      ({
        ccclass
      } = _decorator);

      _export("eEntityTag", eEntityTag = /*#__PURE__*/function (eEntityTag) {
        eEntityTag[eEntityTag["None"] = 0] = "None";
        eEntityTag[eEntityTag["Player"] = 1] = "Player";
        eEntityTag[eEntityTag["Enemy"] = 2] = "Enemy";
        eEntityTag[eEntityTag["PlayerBullet"] = 4] = "PlayerBullet";
        eEntityTag[eEntityTag["EnemyBullet"] = 8] = "EnemyBullet";
        eEntityTag[eEntityTag["Boss"] = 16] = "Boss";
        return eEntityTag;
      }({}));

      _export("default", Entity = (_dec = ccclass('Entity'), _dec(_class = class Entity extends Component {
        constructor() {
          super(...arguments);
          this.isDead = false;
          // 是否死亡
          this.m_comps = new Map();
          // 存储组件的 Map
          this.m_tags = eEntityTag.None;
        }

        init() {
          this.initComps();
        }

        updateGameLogic(dt) {}

        initComps() {
          this.m_comps.forEach(comp => {
            comp.init(this);
          });
        }

        addTag(tag) {
          this.m_tags |= tag;
        }

        removeTag(tag) {
          this.m_tags &= ~tag;
        }

        hasTag(tag) {
          return (this.m_tags & tag) !== 0;
        }

        clearTags() {
          this.m_tags = eEntityTag.None;
        }
        /**
         * 获取指定类型的组件
         * @param {string} type 组件类型
         * @returns {any} 组件实例
         */


        getComp(type) {
          return this.m_comps.get(type);
        }
        /**
         * 获取指定类型的所有组件
         * @param {string} type 组件类型
         * @returns {any} 组件实例
         */


        getComps(type) {
          return this.m_comps.get(type);
        }
        /**
         * 添加组件
         * @param {string} type 组件类型
         * @param {any} comp 组件实例
         * @returns {any} 添加的组件实例
         */


        addComp(type, comp) {
          this.m_comps.set(type, comp);
          return comp;
        }
        /**
         * 移除指定类型的组件
         * @param {string} type 组件类型
         */


        removeComp(type) {
          var comp = this.getComp(type);

          if (comp) {
            comp.remove();
          }

          this.m_comps.delete(type);
        }
        /**
         * 移除所有组件
         */


        removeAllComp() {
          if (this.m_comps != null) {
            Array.from(this.m_comps.values()).forEach(comp => {
              comp.remove();
            });
            this.m_comps.clear();
          }
        }
        /**
         * 移除除指定组件外的其他组件
         * @param {BaseComp} keepComp 要保留的组件
         */


        removeOtherComps(keepComp) {
          if (this.m_comps != null) {
            this.m_comps.forEach((comp, type) => {
              if (comp !== keepComp) {
                this.m_comps.delete(type);
              }
            });
          }
        }

        onCollide(collision) {}

        onOutScreen() {} // 是否在游戏世界范围内(比屏幕宽一点)
        // 注意这里只用了点来判断，正常来说要知道宽高，用Rect来判断更合理。


        isInScreen() {
          return (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).isPointInBattleView(this.node.position);
        }

        getAttack() {
          return 1;
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=f4a72348f8c94b46a745ef11b6de0fef0d8aee0b.js.map