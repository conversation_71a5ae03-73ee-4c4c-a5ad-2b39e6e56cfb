"[\n  {\n    \"__type__\": \"cc.SceneAsset\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_native\": \"\",\n    \"scene\": {\n      \"__id__\": 1\n    }\n  },\n  {\n    \"__type__\": \"cc.Scene\",\n    \"_name\": \"LevelEditor\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": null,\n    \"_children\": [\n      {\n        \"__id__\": 2\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": {\n      \"__id__\": 279\n    },\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"autoReleaseAssets\": false,\n    \"_globals\": {\n      \"__id__\": 280\n    },\n    \"_id\": \"401efd7e-bd20-4537-a13a-f25e6238c2a9\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Canvas\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 1\n    },\n    \"_children\": [\n      {\n        \"__id__\": 3\n      },\n      {\n        \"__id__\": 5\n      },\n      {\n        \"__id__\": 105\n      },\n      {\n        \"__id__\": 120\n      },\n      {\n        \"__id__\": 134\n      },\n      {\n        \"__id__\": 145\n      },\n      {\n        \"__id__\": 163\n      },\n      {\n        \"__id__\": 167\n      },\n      {\n        \"__id__\": 171\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 271\n      },\n      {\n        \"__id__\": 272\n      },\n      {\n        \"__id__\": 273\n      },\n      {\n        \"__id__\": 274\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 375,\n      \"y\": 667,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"beI88Z2HpFELqR4T5EMHpg\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Camera\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 4\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 1000\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"ebFwiq8gBFaYpqYbdoDODe\"\n  },\n  {\n    \"__type__\": \"cc.Camera\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 3\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_projection\": 0,\n    \"_priority\": 0,\n    \"_fov\": 45,\n    \"_fovAxis\": 0,\n    \"_orthoHeight\": 667,\n    \"_near\": 0,\n    \"_far\": 2000,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_depth\": 1,\n    \"_stencil\": 0,\n    \"_clearFlags\": 7,\n    \"_rect\": {\n      \"__type__\": \"cc.Rect\",\n      \"x\": 0,\n      \"y\": 0,\n      \"width\": 1,\n      \"height\": 1\n    },\n    \"_aperture\": 19,\n    \"_shutter\": 7,\n    \"_iso\": 0,\n    \"_screenScale\": 1,\n    \"_visibility\": 1108344832,\n    \"_targetTexture\": null,\n    \"_postProcess\": null,\n    \"_usePostProcess\": false,\n    \"_cameraType\": -1,\n    \"_trackingType\": 0,\n    \"_id\": \"63WIch3o5BEYRlXzTT0oWc\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"BackgroundLayer\",\n    \"_objFlags\": 512,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [\n      {\n        \"__id__\": 6\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 104\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"46ihxbAfVPT4CTuCqzBBFA\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"layer\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 5\n    },\n    \"_children\": [\n      {\n        \"__id__\": 7\n      },\n      {\n        \"__id__\": 98\n      },\n      {\n        \"__id__\": 99\n      },\n      {\n        \"__id__\": 100\n      },\n      {\n        \"__id__\": 101\n      },\n      {\n        \"__id__\": 102\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 103\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": -140.5999999999999,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"easmAjwIJKH6IDvaEb4Ufg\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"backgrounds\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 6\n    },\n    \"_children\": [\n      {\n        \"__id__\": 8\n      },\n      {\n        \"__id__\": 13\n      },\n      {\n        \"__id__\": 18\n      },\n      {\n        \"__id__\": 23\n      },\n      {\n        \"__id__\": 28\n      },\n      {\n        \"__id__\": 33\n      },\n      {\n        \"__id__\": 38\n      },\n      {\n        \"__id__\": 43\n      },\n      {\n        \"__id__\": 48\n      },\n      {\n        \"__id__\": 53\n      },\n      {\n        \"__id__\": 58\n      },\n      {\n        \"__id__\": 63\n      },\n      {\n        \"__id__\": 68\n      },\n      {\n        \"__id__\": 73\n      },\n      {\n        \"__id__\": 78\n      },\n      {\n        \"__id__\": 83\n      },\n      {\n        \"__id__\": 88\n      },\n      {\n        \"__id__\": 93\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"546RktWlJAGqk/aYTh8/kg\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 9\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 8\n    },\n    \"asset\": {\n      \"__uuid__\": \"05acd730-ed4b-4ebb-b777-9a03fd82e065\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"7cPl9hdDJA0rs/cPX69UoC\",\n    \"instance\": {\n      \"__id__\": 10\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"630lQnArNAwpiyB/1LPZSo\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 11\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 12\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": -155,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"7cPl9hdDJA0rs/cPX69UoC\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 14\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 13\n    },\n    \"asset\": {\n      \"__uuid__\": \"05acd730-ed4b-4ebb-b777-9a03fd82e065\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"7cPl9hdDJA0rs/cPX69UoC\",\n    \"instance\": {\n      \"__id__\": 15\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"10b18fA+5EM5O4tFJsdKPi\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 16\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 17\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 869,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"7cPl9hdDJA0rs/cPX69UoC\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 19\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 18\n    },\n    \"asset\": {\n      \"__uuid__\": \"05acd730-ed4b-4ebb-b777-9a03fd82e065\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"7cPl9hdDJA0rs/cPX69UoC\",\n    \"instance\": {\n      \"__id__\": 20\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"183gbG3XtHeYteNXJAGeRl\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 21\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 22\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 1893,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"7cPl9hdDJA0rs/cPX69UoC\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 24\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 23\n    },\n    \"asset\": {\n      \"__uuid__\": \"05acd730-ed4b-4ebb-b777-9a03fd82e065\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"7cPl9hdDJA0rs/cPX69UoC\",\n    \"instance\": {\n      \"__id__\": 25\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"219C3vIUNJQ5pxzN89eQvw\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 26\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 27\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 2917,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"7cPl9hdDJA0rs/cPX69UoC\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 29\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 28\n    },\n    \"asset\": {\n      \"__uuid__\": \"05acd730-ed4b-4ebb-b777-9a03fd82e065\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"7cPl9hdDJA0rs/cPX69UoC\",\n    \"instance\": {\n      \"__id__\": 30\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"2e+bjFlb5PD7WWGN6T68aW\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 31\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 32\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 3941,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"7cPl9hdDJA0rs/cPX69UoC\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 34\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 33\n    },\n    \"asset\": {\n      \"__uuid__\": \"05acd730-ed4b-4ebb-b777-9a03fd82e065\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"7cPl9hdDJA0rs/cPX69UoC\",\n    \"instance\": {\n      \"__id__\": 35\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"973O6zc/tDsbB9w0qEuem/\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 36\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 37\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 4965,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"7cPl9hdDJA0rs/cPX69UoC\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 39\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 38\n    },\n    \"asset\": {\n      \"__uuid__\": \"05acd730-ed4b-4ebb-b777-9a03fd82e065\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"7cPl9hdDJA0rs/cPX69UoC\",\n    \"instance\": {\n      \"__id__\": 40\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"c6Y8GfYwZC3JIBdUKoGRqm\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 41\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 42\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 5989,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"7cPl9hdDJA0rs/cPX69UoC\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 44\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 43\n    },\n    \"asset\": {\n      \"__uuid__\": \"05acd730-ed4b-4ebb-b777-9a03fd82e065\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"7cPl9hdDJA0rs/cPX69UoC\",\n    \"instance\": {\n      \"__id__\": 45\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"4bw8UzRidC9LDjK68KEKJh\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 46\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 47\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 7013,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"7cPl9hdDJA0rs/cPX69UoC\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 49\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 48\n    },\n    \"asset\": {\n      \"__uuid__\": \"05acd730-ed4b-4ebb-b777-9a03fd82e065\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"7cPl9hdDJA0rs/cPX69UoC\",\n    \"instance\": {\n      \"__id__\": 50\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"f7ufkTDHRIGJtTznsyG5UM\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 51\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 52\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 8037,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"7cPl9hdDJA0rs/cPX69UoC\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 54\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 53\n    },\n    \"asset\": {\n      \"__uuid__\": \"05acd730-ed4b-4ebb-b777-9a03fd82e065\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"7cPl9hdDJA0rs/cPX69UoC\",\n    \"instance\": {\n      \"__id__\": 55\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"6fOJwlvh5J+a0C7fif8ZFN\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 56\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 57\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 9061,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"7cPl9hdDJA0rs/cPX69UoC\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 59\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 58\n    },\n    \"asset\": {\n      \"__uuid__\": \"05acd730-ed4b-4ebb-b777-9a03fd82e065\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"7cPl9hdDJA0rs/cPX69UoC\",\n    \"instance\": {\n      \"__id__\": 60\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"bfSgKZ3bpOwaGOwX0sBwum\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 61\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 62\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 10085,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"7cPl9hdDJA0rs/cPX69UoC\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 64\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 63\n    },\n    \"asset\": {\n      \"__uuid__\": \"05acd730-ed4b-4ebb-b777-9a03fd82e065\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"7cPl9hdDJA0rs/cPX69UoC\",\n    \"instance\": {\n      \"__id__\": 65\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"c7yqr2rXpFg7yY69yQ4Nyv\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 66\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 67\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 11109,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"7cPl9hdDJA0rs/cPX69UoC\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 69\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 68\n    },\n    \"asset\": {\n      \"__uuid__\": \"05acd730-ed4b-4ebb-b777-9a03fd82e065\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"7cPl9hdDJA0rs/cPX69UoC\",\n    \"instance\": {\n      \"__id__\": 70\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"08iHW/Z1RHbokrNVilzgVu\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 71\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 72\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 12133,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"7cPl9hdDJA0rs/cPX69UoC\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 74\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 73\n    },\n    \"asset\": {\n      \"__uuid__\": \"05acd730-ed4b-4ebb-b777-9a03fd82e065\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"7cPl9hdDJA0rs/cPX69UoC\",\n    \"instance\": {\n      \"__id__\": 75\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"d0b54ReqRJAax5xktybDTa\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 76\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 77\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 13157,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"7cPl9hdDJA0rs/cPX69UoC\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 79\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 78\n    },\n    \"asset\": {\n      \"__uuid__\": \"05acd730-ed4b-4ebb-b777-9a03fd82e065\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"7cPl9hdDJA0rs/cPX69UoC\",\n    \"instance\": {\n      \"__id__\": 80\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"e1dBbf7jlKibRdmaJKsB2/\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 81\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 82\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 14181,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"7cPl9hdDJA0rs/cPX69UoC\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 84\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 83\n    },\n    \"asset\": {\n      \"__uuid__\": \"05acd730-ed4b-4ebb-b777-9a03fd82e065\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"7cPl9hdDJA0rs/cPX69UoC\",\n    \"instance\": {\n      \"__id__\": 85\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"2dwOs6xJJMaIV0MHj1s4PV\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 86\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 87\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 15205,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"7cPl9hdDJA0rs/cPX69UoC\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 89\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 88\n    },\n    \"asset\": {\n      \"__uuid__\": \"05acd730-ed4b-4ebb-b777-9a03fd82e065\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"7cPl9hdDJA0rs/cPX69UoC\",\n    \"instance\": {\n      \"__id__\": 90\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"29jM1iPCtFka82+McJdQYN\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 91\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 92\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 16229,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"7cPl9hdDJA0rs/cPX69UoC\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 94\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 93\n    },\n    \"asset\": {\n      \"__uuid__\": \"05acd730-ed4b-4ebb-b777-9a03fd82e065\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"7cPl9hdDJA0rs/cPX69UoC\",\n    \"instance\": {\n      \"__id__\": 95\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"cdD8/5WNpDZLPScE+7TI+Q\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 96\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 97\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 17253,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"7cPl9hdDJA0rs/cPX69UoC\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"terrains\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 6\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"665HT+F6JBc7xoQCcMy01q\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"scrolls\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 6\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"5bkj97K7hH1rwKpPElUs5J\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"dynamic\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 6\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"1dj243Sp5BwIlKNgb6O1n5\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"emittiers\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 6\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"d6hQRqFUpDVaVMpi1NMF9B\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"events\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 6\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"a74+RfsaVAxbHSXm69UQ2c\"\n  },\n  {\n    \"__type__\": \"92d5epe+XRMm6NUwiCSJBKR\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 6\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_id\": \"151Vzc0QhCE55OzTCe511x\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 5\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 100,\n      \"height\": 100\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"c7cr3UHz9ID5OL86RVgF3A\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"FloorLayers\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [\n      {\n        \"__id__\": 106\n      },\n      {\n        \"__id__\": 113\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"62oMpiqNVJeLfnCzN4mnAI\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"layer_1\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 105\n    },\n    \"_children\": [\n      {\n        \"__id__\": 107\n      },\n      {\n        \"__id__\": 108\n      },\n      {\n        \"__id__\": 109\n      },\n      {\n        \"__id__\": 110\n      },\n      {\n        \"__id__\": 111\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 112\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": -147.6299999999999,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"37ttM+uxZHV4XuX66RI9HF\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"terrains\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 106\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"52vac0IGlMK6tYt3776q1b\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"scrolls\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 106\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"16yQA7P6FAq72VV3c4h29f\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"dynamic\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 106\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"f1sdgwPcRHyZGpy1FD1NO1\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"emittiers\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 106\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"62SEO3j0tB1oeaZiLZOsEX\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"events\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 106\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"faIa3OSrlHVorpofE/6amQ\"\n  },\n  {\n    \"__type__\": \"92d5epe+XRMm6NUwiCSJBKR\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 106\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_id\": \"08/36hCvFEur4bpHboYqFt\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"layer_0\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 105\n    },\n    \"_children\": [\n      {\n        \"__id__\": 114\n      },\n      {\n        \"__id__\": 115\n      },\n      {\n        \"__id__\": 116\n      },\n      {\n        \"__id__\": 117\n      },\n      {\n        \"__id__\": 118\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 119\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": -7.029999999999996,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"61xZIUsQhJoJ0t59feQWDZ\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"terrains\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 113\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"550QGX9t1Fyao3MjiXxAyI\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"scrolls\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 113\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"babQ1gl79JgLutE4ygfLMQ\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"dynamic\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 113\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"16aiYU+3FEXaPeFpSRrSFz\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"emittiers\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 113\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"a9E6Rbu59NH51u4wUFFgzK\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"events\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 113\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"18YipCBT9EZKIrt30q1gET\"\n  },\n  {\n    \"__type__\": \"92d5epe+XRMm6NUwiCSJBKR\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 113\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_id\": \"046vU5VIxGX5dGu2YwrAUG\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"MainPlane\",\n    \"_objFlags\": 512,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [\n      {\n        \"__id__\": 121\n      },\n      {\n        \"__id__\": 123\n      },\n      {\n        \"__id__\": 128\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 133\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"c5Llji/3pG8ZUsk+/AnCts\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"enemy\",\n    \"_objFlags\": 512,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 120\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 122\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"22ge7J4Q5BcrDOrtqtJWcy\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 121\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 100,\n      \"height\": 100\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"0e4ol/pP9AdL9y81NxK1IW\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Plane 128\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 120\n    },\n    \"_children\": [\n      {\n        \"__id__\": 124\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 127\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"dbYdiDAGtJNILT2mAHeQHg\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"128\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 123\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 125\n      },\n      {\n        \"__id__\": 126\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -146.727,\n      \"y\": -381.491,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"b1innlyJpPfbCEAHtzu7nR\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 124\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 126,\n      \"height\": 106\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"fcvUM8lEVG7r+Duly9xTUi\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 124\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"707afdf7-8acc-4d9b-9afb-ed259ef05bdc@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"b9z9YmlO1ORJddzAvkDa26\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 123\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 100,\n      \"height\": 100\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"69CWNsBiBF8qLjUsjYBXVD\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Plane 150\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 120\n    },\n    \"_children\": [\n      {\n        \"__id__\": 129\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 132\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"8fSyYswbxJMYFuKxLm+oIa\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"150\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 128\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 130\n      },\n      {\n        \"__id__\": 131\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 49.887,\n      \"y\": -384.426,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"97bvio7axI36wm/98rDd6y\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 129\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 148,\n      \"height\": 124\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"4c8v2GIyBL7p+TWWFkd8E0\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 129\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"8aa182ba-4aa2-40b8-b54a-53dd0462aefe@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"c3kMW+vo1DD4E5mvwzvbme\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 128\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 100,\n      \"height\": 100\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"7dfuqEV4hA5JIX88WPdF7X\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 120\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 100,\n      \"height\": 100\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"2dVZOPnxBB55oxOGHxThGs\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"SkyLayers\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [\n      {\n        \"__id__\": 135\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"a3kr0ELyxNI6WeWVx+sGNb\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"layer_0\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 134\n    },\n    \"_children\": [\n      {\n        \"__id__\": 136\n      },\n      {\n        \"__id__\": 137\n      },\n      {\n        \"__id__\": 138\n      },\n      {\n        \"__id__\": 139\n      },\n      {\n        \"__id__\": 143\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 144\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"e0sYax6HZFUruqflR9ge4C\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"terrains\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 135\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"b4XZb/ZztAxZToffZJaOA0\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"scrolls\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 135\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"7d+PU7pA5DboBzSMgEXHyH\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"dynamic\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 135\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"c7y8abjCREH7131a4gd+an\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"emittiers\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 135\n    },\n    \"_children\": [\n      {\n        \"__id__\": 140\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"47Vi+u+79HV4ZlLAw5RYVY\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 139\n    },\n    \"_prefab\": {\n      \"__id__\": 141\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 140\n    },\n    \"asset\": {\n      \"__uuid__\": \"dcbec7b6-e289-48d4-ada6-f451bfef36da\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"3c4/l6yJJHU4M9UaoyAHFC\",\n    \"instance\": {\n      \"__id__\": 142\n    },\n    \"targetOverrides\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"fduQbretpGTYcBhAF5d/cd\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"events\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 135\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"12E3lrNOJFsrhIDEOUfqIa\"\n  },\n  {\n    \"__type__\": \"92d5epe+XRMm6NUwiCSJBKR\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 135\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_id\": \"04dVj+V4lKt6DbiZxa03nw\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"DrawNode\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [\n      {\n        \"__id__\": 146\n      },\n      {\n        \"__id__\": 149\n      },\n      {\n        \"__id__\": 152\n      },\n      {\n        \"__id__\": 155\n      },\n      {\n        \"__id__\": 158\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 161\n      },\n      {\n        \"__id__\": 162\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"33t5ymWlpLkbT5idbqGQ7X\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"drawViewTotal\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 145\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 147\n      },\n      {\n        \"__id__\": 148\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"71DdTPOl1GapOSSW9RVix5\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 146\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 100,\n      \"height\": 100\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"9bs9fYFFdAcqhbMrSc5Km1\"\n  },\n  {\n    \"__type__\": \"cc.Graphics\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 146\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_lineWidth\": 6,\n    \"_strokeColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_lineJoin\": 2,\n    \"_lineCap\": 0,\n    \"_fillColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_miterLimit\": 10,\n    \"_id\": \"91zUXrRYJGm4o6FQWDPvPO\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"drawView\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 145\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 150\n      },\n      {\n        \"__id__\": 151\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"65LbppaIRGz7EVQ8Ktul+X\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 149\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 100,\n      \"height\": 100\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"b1Cf74eydN0Yy5qbbxInaR\"\n  },\n  {\n    \"__type__\": \"cc.Graphics\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 149\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_lineWidth\": 8,\n    \"_strokeColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_lineJoin\": 2,\n    \"_lineCap\": 0,\n    \"_fillColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_miterLimit\": 10,\n    \"_id\": \"75zN+z8lVGqLYHx2jxa0qd\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"drawMask\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 145\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 153\n      },\n      {\n        \"__id__\": 154\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"9btiDQ+VZN95aFsLmdMyGC\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 152\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 100,\n      \"height\": 100\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"9eg7PH/u9Il7l5kqhTVfqD\"\n  },\n  {\n    \"__type__\": \"cc.Graphics\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 152\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_lineWidth\": 1,\n    \"_strokeColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_lineJoin\": 2,\n    \"_lineCap\": 0,\n    \"_fillColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_miterLimit\": 10,\n    \"_id\": \"f7nWk9GTBEfo/KF0FlisGD\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"time\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 145\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 156\n      },\n      {\n        \"__id__\": 157\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 458.235,\n      \"y\": 621.287,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"03lbeQRQBBaJmCmQbmnIP1\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 155\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 400,\n      \"height\": 70\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0.5\n    },\n    \"_id\": \"0aPFKUoHhJCY99iaVm3K/C\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 155\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_string\": \"时间：0.70\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 61,\n    \"_fontSize\": 60,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 60,\n    \"_overflow\": 2,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": false,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 2,\n      \"y\": 2\n    },\n    \"_shadowBlur\": 2,\n    \"_id\": \"d7uTBlts9OhJ188cabfoNE\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"progress\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 145\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 159\n      },\n      {\n        \"__id__\": 160\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 458.235,\n      \"y\": 454.781,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"7cW5DvaP9E7bNc04cxSN9/\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 158\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 400,\n      \"height\": 70\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0.5\n    },\n    \"_id\": \"d5aSmlRnRDlJwqRXq14CD4\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 158\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_string\": \"进度：0.01\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 61,\n    \"_fontSize\": 60,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 60,\n    \"_overflow\": 2,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": false,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 2,\n      \"y\": 2\n    },\n    \"_shadowBlur\": 2,\n    \"_id\": \"038VoLC5NCxYX7kf/kDiDH\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 145\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 850,\n      \"height\": 1334\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"9cewjtt/5IRbxIivL8bKq5\"\n  },\n  {\n    \"__type__\": \"cc.Graphics\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 145\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_lineWidth\": 6,\n    \"_strokeColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_lineJoin\": 2,\n    \"_lineCap\": 0,\n    \"_fillColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_miterLimit\": 10,\n    \"_id\": \"23xs7GFb1G6bRSeyAo5w9K\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"GizmoManager\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 164\n      },\n      {\n        \"__id__\": 165\n      },\n      {\n        \"__id__\": 166\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"23TQ+5yTNAyK7H6psLGXWg\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 163\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 100,\n      \"height\": 100\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"f9jCsZET9L+Ybd8UX16F6R\"\n  },\n  {\n    \"__type__\": \"35b7e0iBnFHtqqvAd1SurM7\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 163\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"gizmosEnabled\": true,\n    \"drawInPlayMode\": false,\n    \"refreshRate\": 60,\n    \"maxDrawDistance\": 2000,\n    \"_id\": \"c15PFCb7JLcrLysr6F+DtZ\"\n  },\n  {\n    \"__type__\": \"cc.Graphics\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 163\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_lineWidth\": 20,\n    \"_strokeColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_lineJoin\": 2,\n    \"_lineCap\": 0,\n    \"_fillColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_miterLimit\": 10,\n    \"_id\": \"85LkKkzdVB45KD5PBufbQj\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"WavePreview\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 168\n      },\n      {\n        \"__id__\": 169\n      },\n      {\n        \"__id__\": 170\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"97hK/8rTxLfrLJQXLgQpZi\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 167\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 100,\n      \"height\": 100\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"21ZgnNk/FGEYxocAGfy5xD\"\n  },\n  {\n    \"__type__\": \"e6727UuHORMv49xH6m7Wz2U\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 167\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_id\": \"96jNiemDdM35UIwxyvgTsh\"\n  },\n  {\n    \"__type__\": \"cc.Graphics\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 167\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_lineWidth\": 1,\n    \"_strokeColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_lineJoin\": 2,\n    \"_lineCap\": 0,\n    \"_fillColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_miterLimit\": 10,\n    \"_id\": \"27LBU+1fxHsZGbGZ9B+5FR\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"EventNodeParent\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [\n      {\n        \"__id__\": 172\n      },\n      {\n        \"__id__\": 185\n      },\n      {\n        \"__id__\": 201\n      },\n      {\n        \"__id__\": 217\n      },\n      {\n        \"__id__\": 233\n      },\n      {\n        \"__id__\": 249\n      },\n      {\n        \"__id__\": 265\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"d0kz6eOcZGCpFamFcaqV2X\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"background_0_event\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 171\n    },\n    \"_children\": [\n      {\n        \"__id__\": 173\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 180\n      },\n      {\n        \"__id__\": 183\n      },\n      {\n        \"__id__\": 184\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -399.448,\n      \"y\": 1939.6490000000003,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"f78Ht/JLpN+Kg6XOXLtboY\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 172\n    },\n    \"_prefab\": {\n      \"__id__\": 174\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 173\n    },\n    \"asset\": {\n      \"__uuid__\": \"c1e00235-c1aa-4f56-93ea-d23d344c3e7e\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"f1iE1tJbZERL2ADSYjQJQe\",\n    \"instance\": {\n      \"__id__\": 175\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"1dQcKqjAlJvpt8VYzMW7mN\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [\n      {\n        \"__id__\": 176\n      }\n    ],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 178\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.MountedChildrenInfo\",\n    \"targetInfo\": {\n      \"__id__\": 177\n    },\n    \"nodes\": []\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"f1iE1tJbZERL2ADSYjQJQe\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 179\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 337.794,\n      \"y\": 0,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"f1iE1tJbZERL2ADSYjQJQe\"\n    ]\n  },\n  {\n    \"__type__\": \"9fa8bQG+gZMmpqlZsjy9rl1\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 172\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"remark\": \"\",\n    \"conditions\": [\n      {\n        \"__id__\": 181\n      }\n    ],\n    \"triggers\": [\n      {\n        \"__id__\": 182\n      }\n    ],\n    \"_id\": \"69E/kYaEtAwL9mN+k8YuTh\"\n  },\n  {\n    \"__type__\": \"LevelEditorCondition\"\n  },\n  {\n    \"__type__\": \"LevelEditorEventTrigger\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 172\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 256,\n      \"height\": 256\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"9dGDdDi/JIWJ2ANbMBqDtc\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 172\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": null,\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"9aVXXJERBFIrHPJue2bjWz\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"floor_1_event\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 171\n    },\n    \"_children\": [\n      {\n        \"__id__\": 186\n      },\n      {\n        \"__id__\": 191\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 196\n      },\n      {\n        \"__id__\": 199\n      },\n      {\n        \"__id__\": 200\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -15.492999999999995,\n      \"y\": 2553.553,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"7awh7ntaJEpYC8TXZRYfpm\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 185\n    },\n    \"_prefab\": {\n      \"__id__\": 187\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 186\n    },\n    \"asset\": {\n      \"__uuid__\": \"022be03c-f31f-4810-8364-c3e055cce9f8\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 188\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"9dIDeToctKRIOpjY/dKzTT\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [\n      {\n        \"__id__\": 189\n      }\n    ],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.MountedChildrenInfo\",\n    \"targetInfo\": {\n      \"__id__\": 190\n    },\n    \"nodes\": []\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"c46/YsCPVOJYA4mWEpNYRx\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 185\n    },\n    \"_prefab\": {\n      \"__id__\": 192\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 191\n    },\n    \"asset\": {\n      \"__uuid__\": \"275c8251-43c9-4d18-84b5-7d2bc1fe41ca\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 193\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"4d4qw+AsdDHrozZ/axr9hF\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [\n      {\n        \"__id__\": 194\n      }\n    ],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.MountedChildrenInfo\",\n    \"targetInfo\": {\n      \"__id__\": 195\n    },\n    \"nodes\": []\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"c46/YsCPVOJYA4mWEpNYRx\"\n    ]\n  },\n  {\n    \"__type__\": \"9fa8bQG+gZMmpqlZsjy9rl1\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 185\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"remark\": \"\",\n    \"conditions\": [\n      {\n        \"__id__\": 197\n      }\n    ],\n    \"triggers\": [\n      {\n        \"__id__\": 198\n      }\n    ],\n    \"_id\": \"59bvdn/XhGM5WO4rSfpj8R\"\n  },\n  {\n    \"__type__\": \"LevelEditorCondition\"\n  },\n  {\n    \"__type__\": \"LevelEditorEventTrigger\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 185\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 256,\n      \"height\": 256\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"c3OsCcU9BHCrgZliRPyp0i\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 185\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": null,\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"59A8Fcq+5HaKQsA6iqP3kk\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"floor_1_event\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 171\n    },\n    \"_children\": [\n      {\n        \"__id__\": 202\n      },\n      {\n        \"__id__\": 207\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 212\n      },\n      {\n        \"__id__\": 215\n      },\n      {\n        \"__id__\": 216\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -15.493,\n      \"y\": 1460.486,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"7780nYWPZHor//Bc/FB4se\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 201\n    },\n    \"_prefab\": {\n      \"__id__\": 203\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 202\n    },\n    \"asset\": {\n      \"__uuid__\": \"da722847-bca5-434a-9e6c-da041e17eea1\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 204\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"b3+xEyVm1N9Ywiv5F6zMS6\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [\n      {\n        \"__id__\": 205\n      }\n    ],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.MountedChildrenInfo\",\n    \"targetInfo\": {\n      \"__id__\": 206\n    },\n    \"nodes\": []\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"c46/YsCPVOJYA4mWEpNYRx\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 201\n    },\n    \"_prefab\": {\n      \"__id__\": 208\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 207\n    },\n    \"asset\": {\n      \"__uuid__\": \"b67c2291-c230-46b7-9a5f-8b6107fe9389\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 209\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"11HAz6+XFH1qjpTQwAPlTs\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [\n      {\n        \"__id__\": 210\n      }\n    ],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.MountedChildrenInfo\",\n    \"targetInfo\": {\n      \"__id__\": 211\n    },\n    \"nodes\": []\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"c46/YsCPVOJYA4mWEpNYRx\"\n    ]\n  },\n  {\n    \"__type__\": \"9fa8bQG+gZMmpqlZsjy9rl1\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 201\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"remark\": \"\",\n    \"conditions\": [\n      {\n        \"__id__\": 213\n      }\n    ],\n    \"triggers\": [\n      {\n        \"__id__\": 214\n      }\n    ],\n    \"_id\": \"a3OUrsidpBH6tjKByM69q2\"\n  },\n  {\n    \"__type__\": \"LevelEditorCondition\"\n  },\n  {\n    \"__type__\": \"LevelEditorEventTrigger\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 201\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 256,\n      \"height\": 256\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"57qmz1tuJLfaBpN1l0WRBO\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 201\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": null,\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"34+VYnffBGkrD22mdAsJzv\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"floor_1_event\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 171\n    },\n    \"_children\": [\n      {\n        \"__id__\": 218\n      },\n      {\n        \"__id__\": 223\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 228\n      },\n      {\n        \"__id__\": 231\n      },\n      {\n        \"__id__\": 232\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -35.724,\n      \"y\": 4145.3,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"00yStUh1NE34C9j4PWlsVP\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 217\n    },\n    \"_prefab\": {\n      \"__id__\": 219\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 218\n    },\n    \"asset\": {\n      \"__uuid__\": \"a707b99a-b72d-479b-a22b-1cbd36642f6a\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 220\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"4cgHytqdFPU5BxQZ4ixXIP\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [\n      {\n        \"__id__\": 221\n      }\n    ],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.MountedChildrenInfo\",\n    \"targetInfo\": {\n      \"__id__\": 222\n    },\n    \"nodes\": []\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"c46/YsCPVOJYA4mWEpNYRx\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 217\n    },\n    \"_prefab\": {\n      \"__id__\": 224\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 223\n    },\n    \"asset\": {\n      \"__uuid__\": \"df1eb211-463c-4c96-b987-b192ba91073c\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 225\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"acZO+V9/hNlqiX2kN+wTwS\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [\n      {\n        \"__id__\": 226\n      }\n    ],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.MountedChildrenInfo\",\n    \"targetInfo\": {\n      \"__id__\": 227\n    },\n    \"nodes\": []\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"c46/YsCPVOJYA4mWEpNYRx\"\n    ]\n  },\n  {\n    \"__type__\": \"9fa8bQG+gZMmpqlZsjy9rl1\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 217\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"remark\": \"\",\n    \"conditions\": [\n      {\n        \"__id__\": 229\n      }\n    ],\n    \"triggers\": [\n      {\n        \"__id__\": 230\n      }\n    ],\n    \"_id\": \"66bIy20fZIW70PPVdUMxQB\"\n  },\n  {\n    \"__type__\": \"LevelEditorCondition\"\n  },\n  {\n    \"__type__\": \"LevelEditorEventTrigger\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 217\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 256,\n      \"height\": 256\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"16k8qBLiJIPYY7Uirp4kuu\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 217\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": null,\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"63fFggpfNAf69+0o+yydqr\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"floor_1_event\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 171\n    },\n    \"_children\": [\n      {\n        \"__id__\": 234\n      },\n      {\n        \"__id__\": 239\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 244\n      },\n      {\n        \"__id__\": 247\n      },\n      {\n        \"__id__\": 248\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -35.72399999999999,\n      \"y\": 479.3040000000001,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"06mpWHTDxLn44rEyynHa8n\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 233\n    },\n    \"_prefab\": {\n      \"__id__\": 235\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 234\n    },\n    \"asset\": {\n      \"__uuid__\": \"a707b99a-b72d-479b-a22b-1cbd36642f6a\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 236\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"59/Qj0g9hKL51oQ6NPAogQ\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [\n      {\n        \"__id__\": 237\n      }\n    ],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.MountedChildrenInfo\",\n    \"targetInfo\": {\n      \"__id__\": 238\n    },\n    \"nodes\": []\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"c46/YsCPVOJYA4mWEpNYRx\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 233\n    },\n    \"_prefab\": {\n      \"__id__\": 240\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 239\n    },\n    \"asset\": {\n      \"__uuid__\": \"df1eb211-463c-4c96-b987-b192ba91073c\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 241\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"03GsohKUdPxIuZBl9H5P3W\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [\n      {\n        \"__id__\": 242\n      }\n    ],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.MountedChildrenInfo\",\n    \"targetInfo\": {\n      \"__id__\": 243\n    },\n    \"nodes\": []\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"c46/YsCPVOJYA4mWEpNYRx\"\n    ]\n  },\n  {\n    \"__type__\": \"9fa8bQG+gZMmpqlZsjy9rl1\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 233\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"remark\": \"\",\n    \"conditions\": [\n      {\n        \"__id__\": 245\n      }\n    ],\n    \"triggers\": [\n      {\n        \"__id__\": 246\n      }\n    ],\n    \"_id\": \"10GIWGFH5ENbA9YmOQxzxK\"\n  },\n  {\n    \"__type__\": \"LevelEditorCondition\"\n  },\n  {\n    \"__type__\": \"LevelEditorEventTrigger\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 233\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 256,\n      \"height\": 256\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"adZoJx/EZNV7SEtPzqK8k4\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 233\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": null,\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"52mewVh1tFwrRSqT1CDCFZ\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"floor_1_event\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 171\n    },\n    \"_children\": [\n      {\n        \"__id__\": 250\n      },\n      {\n        \"__id__\": 255\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 260\n      },\n      {\n        \"__id__\": 263\n      },\n      {\n        \"__id__\": 264\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -15.493,\n      \"y\": 1979.6209999999999,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"36jyw5+FRALriD2IF9B/+Q\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 249\n    },\n    \"_prefab\": {\n      \"__id__\": 251\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 250\n    },\n    \"asset\": {\n      \"__uuid__\": \"da722847-bca5-434a-9e6c-da041e17eea1\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 252\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"a3LjGNZ71GXoFviNlZaLGv\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [\n      {\n        \"__id__\": 253\n      }\n    ],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.MountedChildrenInfo\",\n    \"targetInfo\": {\n      \"__id__\": 254\n    },\n    \"nodes\": []\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"c46/YsCPVOJYA4mWEpNYRx\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 249\n    },\n    \"_prefab\": {\n      \"__id__\": 256\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 255\n    },\n    \"asset\": {\n      \"__uuid__\": \"b67c2291-c230-46b7-9a5f-8b6107fe9389\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 257\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"70Z7LuhGFKm59R/15fVPnu\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [\n      {\n        \"__id__\": 258\n      }\n    ],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.MountedChildrenInfo\",\n    \"targetInfo\": {\n      \"__id__\": 259\n    },\n    \"nodes\": []\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"c46/YsCPVOJYA4mWEpNYRx\"\n    ]\n  },\n  {\n    \"__type__\": \"9fa8bQG+gZMmpqlZsjy9rl1\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 249\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"remark\": \"\",\n    \"conditions\": [\n      {\n        \"__id__\": 261\n      }\n    ],\n    \"triggers\": [\n      {\n        \"__id__\": 262\n      }\n    ],\n    \"_id\": \"fc00Ur9a9Gsr+gf1dnUd65\"\n  },\n  {\n    \"__type__\": \"LevelEditorCondition\"\n  },\n  {\n    \"__type__\": \"LevelEditorEventTrigger\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 249\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 256,\n      \"height\": 256\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"9anAIkMd1ABYiFJ3W8WWKP\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 249\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": null,\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"21PauAQ+pBVb9tReZqdpt8\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"floor_1_LevelFinish\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 171\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 266\n      },\n      {\n        \"__id__\": 269\n      },\n      {\n        \"__id__\": 270\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": -319.4099999999999,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"81lNejEBVPFIgMXC/0NGCN\"\n  },\n  {\n    \"__type__\": \"9fa8bQG+gZMmpqlZsjy9rl1\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 265\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"remark\": \"LevelFinish\",\n    \"conditions\": [\n      {\n        \"__id__\": 267\n      }\n    ],\n    \"triggers\": [\n      {\n        \"__id__\": 268\n      }\n    ],\n    \"_id\": \"0b1AEvxHpFMLlWHo5+AAhM\"\n  },\n  {\n    \"__type__\": \"LevelEditorCondition\"\n  },\n  {\n    \"__type__\": \"LevelEditorEventTrigger\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 265\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 256,\n      \"height\": 256\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"42egXxEVJFuYdlJjtWJ7d+\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 265\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": null,\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"c6W4MKuAhOb4kP8fAC62+m\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 2\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 750,\n      \"height\": 1334\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"d6rUX5yfhMlKoWX2bSbawx\"\n  },\n  {\n    \"__type__\": \"cc.Canvas\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 2\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_cameraComponent\": {\n      \"__id__\": 4\n    },\n    \"_alignCanvasWithScreen\": true,\n    \"_id\": \"12O/ljcVlEqLmVm3U2gEOQ\"\n  },\n  {\n    \"__type__\": \"68a25Vb5mhGApMaV59XFjQ0\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 2\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"progress\": 0.007811111111111106,\n    \"_id\": \"61Fg6c5BVE0I4TKZPyCZ+2\"\n  },\n  {\n    \"__type__\": \"a4bf2J2KGJJV7RbX1jwoQWJ\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 2\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"levelname\": \"高空-平原+岛屿\",\n    \"totalTime\": 90000,\n    \"backgroundLayer\": {\n      \"__id__\": 275\n    },\n    \"floorLayers\": [\n      {\n        \"__id__\": 276\n      },\n      {\n        \"__id__\": 277\n      }\n    ],\n    \"skyLayers\": [\n      {\n        \"__id__\": 278\n      }\n    ],\n    \"_id\": \"f2rN/MimJBS6HE7SikeuYU\"\n  },\n  {\n    \"__type__\": \"LevelEditorBackgroundLayer\",\n    \"remark\": \"海面背景\",\n    \"zIndex\": 0,\n    \"node\": {\n      \"__id__\": 6\n    },\n    \"speed\": 200,\n    \"scrollLayers\": [],\n    \"randomLayers\": [],\n    \"emittierLayers\": [],\n    \"backgrounds\": [\n      {\n        \"__uuid__\": \"05acd730-ed4b-4ebb-b777-9a03fd82e065\",\n        \"__expectedType__\": \"cc.Prefab\"\n      }\n    ]\n  },\n  {\n    \"__type__\": \"LevelEditorLayer\",\n    \"remark\": \"\",\n    \"zIndex\": 0,\n    \"node\": {\n      \"__id__\": 113\n    },\n    \"speed\": 10,\n    \"scrollLayers\": [],\n    \"randomLayers\": [],\n    \"emittierLayers\": []\n  },\n  {\n    \"__type__\": \"LevelEditorLayer\",\n    \"remark\": \"\",\n    \"zIndex\": 0,\n    \"node\": {\n      \"__id__\": 106\n    },\n    \"speed\": 210,\n    \"scrollLayers\": [],\n    \"randomLayers\": [],\n    \"emittierLayers\": []\n  },\n  {\n    \"__type__\": \"LevelEditorLayer\",\n    \"remark\": \"高空云层发射器\",\n    \"zIndex\": 0,\n    \"node\": {\n      \"__id__\": 135\n    },\n    \"speed\": 0,\n    \"scrollLayers\": [],\n    \"randomLayers\": [],\n    \"emittierLayers\": [\n      {\n        \"__uuid__\": \"dcbec7b6-e289-48d4-ada6-f451bfef36da\",\n        \"__expectedType__\": \"cc.Prefab\"\n      }\n    ]\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": null,\n    \"asset\": null,\n    \"fileId\": \"401efd7e-bd20-4537-a13a-f25e6238c2a9\",\n    \"instance\": null,\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": [\n      {\n        \"__id__\": 8\n      },\n      {\n        \"__id__\": 13\n      },\n      {\n        \"__id__\": 18\n      },\n      {\n        \"__id__\": 23\n      },\n      {\n        \"__id__\": 28\n      },\n      {\n        \"__id__\": 33\n      },\n      {\n        \"__id__\": 38\n      },\n      {\n        \"__id__\": 43\n      },\n      {\n        \"__id__\": 48\n      },\n      {\n        \"__id__\": 53\n      },\n      {\n        \"__id__\": 58\n      },\n      {\n        \"__id__\": 63\n      },\n      {\n        \"__id__\": 68\n      },\n      {\n        \"__id__\": 73\n      },\n      {\n        \"__id__\": 78\n      },\n      {\n        \"__id__\": 83\n      },\n      {\n        \"__id__\": 88\n      },\n      {\n        \"__id__\": 93\n      },\n      {\n        \"__id__\": 140\n      },\n      {\n        \"__id__\": 173\n      },\n      {\n        \"__id__\": 186\n      },\n      {\n        \"__id__\": 191\n      },\n      {\n        \"__id__\": 202\n      },\n      {\n        \"__id__\": 207\n      },\n      {\n        \"__id__\": 218\n      },\n      {\n        \"__id__\": 223\n      },\n      {\n        \"__id__\": 234\n      },\n      {\n        \"__id__\": 239\n      },\n      {\n        \"__id__\": 250\n      },\n      {\n        \"__id__\": 255\n      }\n    ]\n  },\n  {\n    \"__type__\": \"cc.SceneGlobals\",\n    \"ambient\": {\n      \"__id__\": 281\n    },\n    \"shadows\": {\n      \"__id__\": 282\n    },\n    \"_skybox\": {\n      \"__id__\": 283\n    },\n    \"fog\": {\n      \"__id__\": 284\n    },\n    \"octree\": {\n      \"__id__\": 285\n    },\n    \"skin\": {\n      \"__id__\": 286\n    },\n    \"lightProbeInfo\": {\n      \"__id__\": 287\n    },\n    \"postSettings\": {\n      \"__id__\": 288\n    },\n    \"bakedWithStationaryMainLight\": false,\n    \"bakedWithHighpLightmap\": false\n  },\n  {\n    \"__type__\": \"cc.AmbientInfo\",\n    \"_skyColorHDR\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 0.520833125\n    },\n    \"_skyColor\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 0.520833125\n    },\n    \"_skyIllumHDR\": 20000,\n    \"_skyIllum\": 20000,\n    \"_groundAlbedoHDR\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 0\n    },\n    \"_groundAlbedo\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 0\n    },\n    \"_skyColorLDR\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0.2,\n      \"y\": 0.5,\n      \"z\": 0.8,\n      \"w\": 1\n    },\n    \"_skyIllumLDR\": 20000,\n    \"_groundAlbedoLDR\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0.2,\n      \"y\": 0.2,\n      \"z\": 0.2,\n      \"w\": 1\n    }\n  },\n  {\n    \"__type__\": \"cc.ShadowsInfo\",\n    \"_enabled\": false,\n    \"_type\": 0,\n    \"_normal\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 1,\n      \"z\": 0\n    },\n    \"_distance\": 0,\n    \"_planeBias\": 1,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 76,\n      \"g\": 76,\n      \"b\": 76,\n      \"a\": 255\n    },\n    \"_maxReceived\": 4,\n    \"_size\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 512,\n      \"y\": 512\n    }\n  },\n  {\n    \"__type__\": \"cc.SkyboxInfo\",\n    \"_envLightingType\": 0,\n    \"_envmapHDR\": null,\n    \"_envmap\": null,\n    \"_envmapLDR\": null,\n    \"_diffuseMapHDR\": null,\n    \"_diffuseMapLDR\": null,\n    \"_enabled\": false,\n    \"_useHDR\": true,\n    \"_editableMaterial\": null,\n    \"_reflectionHDR\": null,\n    \"_reflectionLDR\": null,\n    \"_rotationAngle\": 0\n  },\n  {\n    \"__type__\": \"cc.FogInfo\",\n    \"_type\": 0,\n    \"_fogColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 200,\n      \"g\": 200,\n      \"b\": 200,\n      \"a\": 255\n    },\n    \"_enabled\": false,\n    \"_fogDensity\": 0.3,\n    \"_fogStart\": 0.5,\n    \"_fogEnd\": 300,\n    \"_fogAtten\": 5,\n    \"_fogTop\": 1.5,\n    \"_fogRange\": 1.2,\n    \"_accurate\": false\n  },\n  {\n    \"__type__\": \"cc.OctreeInfo\",\n    \"_enabled\": false,\n    \"_minPos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -1024,\n      \"y\": -1024,\n      \"z\": -1024\n    },\n    \"_maxPos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1024,\n      \"y\": 1024,\n      \"z\": 1024\n    },\n    \"_depth\": 8\n  },\n  {\n    \"__type__\": \"cc.SkinInfo\",\n    \"_enabled\": false,\n    \"_blurRadius\": 0.01,\n    \"_sssIntensity\": 3\n  },\n  {\n    \"__type__\": \"cc.LightProbeInfo\",\n    \"_giScale\": 1,\n    \"_giSamples\": 1024,\n    \"_bounces\": 2,\n    \"_reduceRinging\": 0,\n    \"_showProbe\": true,\n    \"_showWireframe\": true,\n    \"_showConvex\": false,\n    \"_data\": null,\n    \"_lightProbeSphereVolume\": 1\n  },\n  {\n    \"__type__\": \"cc.PostSettingsInfo\",\n    \"_toneMappingType\": 0\n  }\n]"