import { _decorator, instantiate, Prefab, Vec2 } from "cc";
import { MyApp } from 'db://assets/bundles/common/script/app/MyApp';
import { Wave } from "../../game/wave/Wave";
import { GameIns } from "../../game/GameIns";
import { LevelDataEventTrigger, LevelDataEventTriggerType } from "./LevelDataEventTrigger";

export class LevelDataEventWave {
    public waveUUID: string = "";
    public waveOffset: Vec2 = new Vec2();

    public fromJSON(obj: any): void {
        Object.assign(this, obj);
    }

    public toJSON(): any {
        return {
            waveUUID: this.waveUUID,
            waveOffset: this.waveOffset,
        };
    }
}

export class LevelDataEventWaveGroup {
    public wave: LevelDataEventWave = new LevelDataEventWave();
    public weight: number = 50;

    public fromJSON(obj: any): void {
        this.wave.fromJSON(obj.wave);
        this.weight = obj.weight;
    }

    public toJSON(): any {
        return {
            wave: this.wave.toJSON(),
            weight: this.weight,
        };
    }
}

export class LevelDataEventTriggerWave extends LevelDataEventTrigger {
    public waveGroup: LevelDataEventWaveGroup[] = [];
    private _isTriggered: boolean = false;
    private _selectedWaveGroup: LevelDataEventWaveGroup | null = null;
    
    constructor() {
        super(LevelDataEventTriggerType.Wave);
    }
    
    public onInit() {
        // 提前创建好wave，但不执行
        if (this.waveGroup.length > 0) {
            let totalWeight = 0;
            this.waveGroup.forEach(waveGroup => {
                totalWeight += waveGroup.weight;
            });

            let randomWeight = Math.floor(GameIns.battleManager.random() * totalWeight);
            let curWeight = 0;
            
            for (let waveGroup of this.waveGroup) {
                curWeight += waveGroup.weight;
                if (randomWeight <= curWeight) {
                    this._selectedWaveGroup = waveGroup;
                    break;
                }
            }
        }
    }

    public onTrigger(x: number, y: number) {
        const waveUUID = this._selectedWaveGroup!.wave.waveUUID;
        const offset = this._selectedWaveGroup!.wave.waveOffset;
        if (waveUUID) {
            const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, waveUUID)
            MyApp.resMgr.load(path, (err: Error | null, prefab: Prefab) => {
                if (err) {
                    console.error('LevelDataEventTriggerWave', " onInit load wave prefab err", err);
                    return;
                }
                const waveComp = instantiate(prefab).getComponent(Wave);
                GameIns.waveManager.addWaveByLevel(waveComp!, offset.x + x, offset.y + y);
            });
        }

        this._isTriggered = true;
    }

    public isWaveSpawned(): boolean {
        if (!this._isTriggered) return false;

        if (this._selectedWaveGroup!.wave.waveUUID) {
            return GameIns.waveManager.isWaveSpawnCompleted(this._selectedWaveGroup!.wave.waveUUID);
        }

        return true;
    }

    public isWaveCleared(): boolean {
        if (!this._isTriggered) return false;

        if (this._selectedWaveGroup!.wave.waveUUID) {
            return GameIns.waveManager.isWaveEnemyDefeated(this._selectedWaveGroup!.wave.waveUUID);
        }

        return true;
    }

    public isWaveTriggered(): boolean {
        return this._isTriggered;
    }

    public fromJSON(obj: any): void {
        this._type = obj._type;

        if (!obj.waveGroup) {
            obj.waveGroup = [];
        } else if (typeof(obj.waveGroup) === 'string') {
            obj.waveGroup = JSON.parse(obj.waveGroup);
        }

        this.waveGroup = obj.waveGroup.map((waveGroup: any) => {
            let newWaveGroup = new LevelDataEventWaveGroup();
            newWaveGroup.fromJSON(waveGroup);
            return newWaveGroup;
        });
    }

    public toJSON(): any {
        // avoid private properties
        return {
            _type: this._type,
            waveGroup: this.waveGroup.map((waveGroup) => waveGroup.toJSON()),
        };
    }
}

