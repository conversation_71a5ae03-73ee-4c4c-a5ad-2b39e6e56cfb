{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/Emitter.ts"], "names": ["_decorator", "assetManager", "misc", "EDITOR", "MyApp", "GameConst", "BulletData", "EmitterData", "Bullet", "BulletProperty", "BulletSystem", "EventGroupContext", "ObjectPool", "PropertyContainerComponent", "log<PERSON>arn", "ccclass", "executeInEditMode", "property", "disallowMultiple", "menu", "degreesToRadians", "radiansToDegrees", "eEmitterStatus", "eEmitterProp", "ePropMask", "Emitter", "displayName", "editor<PERSON><PERSON><PERSON>", "type", "onBulletCreatedCallback", "onEmitterStatusChangedCallback", "isActive", "isOnlyInScreen", "isPreWarm", "isLoop", "initialDelay", "preWarmDuration", "emitDuration", "emitInterval", "emitPower", "loopInterval", "perEmitCount", "perEmitInterval", "perEmitOffsetX", "angle", "count", "arc", "radius", "elapsedTime", "bulletProp", "eventGroups", "_hasAwaken", "_emitterId", "_status", "None", "_statusElapsedTime", "_totalElapsedTime", "_isEmitting", "_nextEmitTime", "_bulletPrefab", "_prewarmEffectPrefab", "_emitEffectPrefab", "_entity", "_emitterConfig", "undefined", "_perEmitBulletQueue", "isEmitting", "status", "statusElapsedTime", "totalElapsedTime", "emitterId", "config", "id", "loadConfigByID", "onLoad", "createProperties", "createEventGroups", "resetProperties", "onDestroy", "length", "for<PERSON>ach", "group", "tryStop", "onLostFocusInEditor", "updatePropertiesInEditor", "emitterData", "value", "eval", "notifyAll", "setIsActive", "active", "notify", "reset", "changeStatus", "setEntity", "entity", "getEntity", "clear", "addProperty", "IsActive", "ElapsedTime", "IsOnlyInScreen", "IsPreWarm", "IsLoop", "InitialDelay", "PrewarmDuration", "EmitDuration", "EmitInterval", "EmitPower", "LoopInterval", "PerEmitCount", "PerEmitInterval", "PerEmitOffsetX", "<PERSON><PERSON>", "Count", "Arc", "<PERSON><PERSON>", "on", "onCreateEmitter", "onDestroyEmitter", "eventGroupData", "ctx", "emitter", "<PERSON><PERSON><PERSON>", "eventGroup", "createEmitterEventGroup", "forceNotify", "resetFromData", "bulletData", "evalProperty", "prop", "canWrite", "ReEval", "isFixedValue", "oldStatus", "Prewarm", "tryStart", "scheduleNextEmit", "startEmitting", "stopEmitting", "unscheduleAllCallbacks", "canEmit", "emit", "j", "targetTime", "i", "push", "index", "perEmitIndex", "emitSingle", "processPerEmitQueue", "nextBullet", "shift", "tryEmit", "direction", "getSpawnDirection", "position", "getSpawnPosition", "createBullet", "angleOffset", "radian", "x", "Math", "cos", "y", "sin", "getEmitOffsetX", "interval", "stepsFromMiddle", "floor", "ceil", "perpendicular", "prefab", "createBulletInEditor", "bullet", "instantiateBullet", "onCreateBullet", "emitterPos", "node", "getWorldPosition", "setWorldPosition", "z", "speedAngle", "atan2", "speed", "onReady", "prefabPath", "Editor", "Message", "request", "then", "uuid", "loadAny", "err", "console", "error", "bulletNode", "getNode", "bulletParent", "getComponent", "destroy", "name", "kBulletNameInEditor", "GetInstance", "lubanMgr", "lubanTables", "TbResEmitter", "get", "playEffect", "rotation", "duration", "effectNode", "setWorldRotation", "scheduleOnce", "returnNode", "isInScreen", "isPointInBattleView", "tick", "deltaTime", "updateStatusNone", "updateStatusPrewarm", "Emitting", "updateStatusEmitting", "Loop<PERSON>ndReached", "updateStatusLoopEndReached", "Completed", "updateStatusCompleted", "wasEmitting"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,Y,OAAAA,Y;AAAcC,MAAAA,I,OAAAA,I;;AAC1BC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,K,iBAAAA,K;;AAIAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,c,iBAAAA,c;;AACRC,MAAAA,Y,iBAAAA,Y;;AACYC,MAAAA,iB,iBAAAA,iB;;AACZC,MAAAA,U,iBAAAA,U;;AACUC,MAAAA,0B,kBAAAA,0B;;AAGVC,MAAAA,O,kBAAAA,O;;;;;;;;;AAET;AACA;OAEM;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,iBAAX;AAA8BC,QAAAA,QAA9B;AAAwCC,QAAAA,gBAAxC;AAA0DC,QAAAA;AAA1D,O,GAAmEnB,U;OACnE;AAAEoB,QAAAA,gBAAF;AAAoBC,QAAAA;AAApB,O,GAAyCnB,I;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;gCACYoB,c,0BAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;eAAAA,c;cAIZ;;;8BACYC,Y,0BAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;eAAAA,Y;;AAQZ;AACA;AACA;AACA;AACA;AACA;;;2BACYC,S,0BAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;eAAAA,S;;;AAQZ;AACA;AACA;AACA;AACA;yBAMaC,O,WALZV,OAAO,CAAC,SAAD,C,UAEPI,IAAI,CAAC,UAAD,C,UACJH,iBAAiB,CAAC,IAAD,C,UACjBE,gBAAgB,CAAC,IAAD,C,UAKZD,QAAQ,CAAC;AAACS,QAAAA,WAAW,EAAE,IAAd;AAAoBC,QAAAA,UAAU,EAAE;AAAhC,OAAD,C,UAKRV,QAAQ,CAAC;AAAEW,QAAAA,IAAI;AAAA;AAAA,sCAAN;AAAqBF,QAAAA,WAAW,EAAE;AAAlC,OAAD,C,UAGRT,QAAQ,CAAC;AAAEW,QAAAA,IAAI;AAAA;AAAA,oCAAN;AAAoBF,QAAAA,WAAW,EAAE;AAAjC,OAAD,C,mFAjBb,MAKaD,OALb;AAAA;AAAA,oEAKsE;AAAA;AAAA;;AAAA;;AAKzB;AAEzC;AACA;AARkE;;AAAA;;AAelE;AAfkE,eAgBlEI,uBAhBkE,GAgBR,IAhBQ;AAAA,eAiBlEC,8BAjBkE,GAiBM,IAjBN;AAmBlE;AAnBkE,eAoB3DC,QApB2D;AAAA,eAqB3DC,cArB2D;AAAA,eAsB3DC,SAtB2D;AAAA,eAuB3DC,MAvB2D;AAAA,eAwB3DC,YAxB2D;AAAA,eAyB3DC,eAzB2D;AAAA,eA0B3DC,YA1B2D;AAAA,eA2B3DC,YA3B2D;AAAA,eA4B3DC,SA5B2D;AAAA,eA6B3DC,YA7B2D;AAAA,eA8B3DC,YA9B2D;AAAA,eA+B3DC,eA/B2D;AAAA,eAgC3DC,cAhC2D;AAAA,eAiC3DC,KAjC2D;AAAA,eAkC3DC,KAlC2D;AAAA,eAmC3DC,GAnC2D;AAAA,eAoC3DC,MApC2D;AAAA,eAqC3DC,WArC2D;AAsClE;AAtCkE,eAuC3DC,UAvC2D;AAyClE;AAzCkE,eA0C3DC,WA1C2D,GA0C/B,EA1C+B;AA4ClE;AA5CkE,eA6CxDC,UA7CwD,GA6ClC,KA7CkC;AAAA,eA8CxDC,UA9CwD,GA8CnC,CA9CmC;AAAA,eA+CxDC,OA/CwD,GA+C9B/B,cAAc,CAACgC,IA/Ce;AAAA,eAgDxDC,kBAhDwD,GAgD3B,CAhD2B;AAAA,eAiDxDC,iBAjDwD,GAiD5B,CAjD4B;AAAA,eAkDxDC,WAlDwD,GAkDjC,KAlDiC;AAAA,eAmDxDC,aAnDwD,GAmDhC,CAnDgC;AAAA,eAqDxDC,aArDwD,GAqDzB,IArDyB;AAAA,eAsDxDC,oBAtDwD,GAsDlB,IAtDkB;AAAA,eAuDxDC,iBAvDwD,GAuDrB,IAvDqB;AAAA,eAwDxDC,OAxDwD,GAwD5B,IAxD4B;AAAA,eAyDxDC,cAzDwD,GAyDfC,SAzDe;AA2DlE;AA3DkE,eA4DxDC,mBA5DwD,GA4DkC,EA5DlC;AAAA;;AA8DpD,YAAVC,UAAU,GAAY;AAAE,iBAAO,KAAKT,WAAZ;AAA0B;;AAC5C,YAANU,MAAM,GAAmB;AAAE,iBAAO,KAAKd,OAAZ;AAAsB;;AAChC,YAAjBe,iBAAiB,GAAW;AAAE,iBAAO,KAAKb,kBAAZ;AAAiC;;AAC/C,YAAhBc,gBAAgB,GAAW;AAAE,iBAAO,KAAKb,iBAAZ;AAAgC;;AACpD,YAATc,SAAS,GAAW;AAAE,iBAAO,KAAKlB,UAAZ;AAAyB;;AACzC,YAANmB,MAAM,GAA2B;AAAE,iBAAO,KAAKR,cAAZ;AAA6B;;AACvD,YAATO,SAAS,CAACE,EAAD,EAAa;AACtB,eAAKpB,UAAL,GAAkBoB,EAAlB;AACA,eAAKC,cAAL,CAAoBD,EAApB;AACH;;AAESE,QAAAA,MAAM,GAAS;AACrB,eAAKC,gBAAL;AACA,eAAKC,iBAAL,GAFqB,CAIrB;;AACA,eAAKC,eAAL,CAAqB,IAArB;AACA,eAAK1B,UAAL,GAAkB,IAAlB;AACH;;AAES2B,QAAAA,SAAS,GAAS;AACxB,eAAK3B,UAAL,GAAkB,KAAlB;;AACA,cAAI,KAAKD,WAAL,CAAiB6B,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B,iBAAK7B,WAAL,CAAiB8B,OAAjB,CAAyBC,KAAK,IAAIA,KAAK,CAACC,OAAN,EAAlC;AACH;AACJ,SAvFiE,CAyFlE;;;AACOC,QAAAA,mBAAmB,GAAS;AAC/B,eAAKC,wBAAL;AACA,eAAKR,iBAAL;AACH;;AAEMQ,QAAAA,wBAAwB,GAAG;AAC9B,cAAI,CAAC,KAAKC,WAAV,EAAuB;AAEvB,eAAKtD,QAAL,CAAcuD,KAAd,GAAsB,IAAtB;AACA,eAAKtD,cAAL,CAAoBsD,KAApB,GAA4B,KAAKD,WAAL,CAAiBrD,cAA7C;AACA,eAAKC,SAAL,CAAeqD,KAAf,GAAuB,KAAKD,WAAL,CAAiBpD,SAAxC;AACA,eAAKC,MAAL,CAAYoD,KAAZ,GAAoB,KAAKD,WAAL,CAAiBnD,MAArC;AAEA,eAAKC,YAAL,CAAkBmD,KAAlB,GAA0B,KAAKD,WAAL,CAAiBlD,YAAjB,CAA8BoD,IAA9B,CAAmC,IAAnC,EAAyC,IAAzC,CAA1B;AACA,eAAKnD,eAAL,CAAqBkD,KAArB,GAA6B,KAAKD,WAAL,CAAiBjD,eAAjB,CAAiCmD,IAAjC,CAAsC,IAAtC,EAA4C,IAA5C,CAA7B;AACA,eAAKlD,YAAL,CAAkBiD,KAAlB,GAA0B,KAAKD,WAAL,CAAiBhD,YAAjB,CAA8BkD,IAA9B,CAAmC,IAAnC,EAAyC,IAAzC,CAA1B;AACA,eAAKjD,YAAL,CAAkBgD,KAAlB,GAA0B,KAAKD,WAAL,CAAiB/C,YAAjB,CAA8BiD,IAA9B,CAAmC,IAAnC,EAAyC,IAAzC,CAA1B;AACA,eAAKhD,SAAL,CAAe+C,KAAf,GAAuB,KAAKD,WAAL,CAAiB9C,SAAjB,CAA2BgD,IAA3B,CAAgC,IAAhC,EAAsC,IAAtC,CAAvB;AACA,eAAK/C,YAAL,CAAkB8C,KAAlB,GAA0B,KAAKD,WAAL,CAAiB7C,YAAjB,CAA8B+C,IAA9B,CAAmC,IAAnC,EAAyC,IAAzC,CAA1B;AACA,eAAK9C,YAAL,CAAkB6C,KAAlB,GAA0B,KAAKD,WAAL,CAAiB5C,YAAjB,CAA8B8C,IAA9B,CAAmC,IAAnC,EAAyC,IAAzC,CAA1B;AACA,eAAK7C,eAAL,CAAqB4C,KAArB,GAA6B,KAAKD,WAAL,CAAiB3C,eAAjB,CAAiC6C,IAAjC,CAAsC,IAAtC,EAA4C,IAA5C,CAA7B;AACA,eAAK5C,cAAL,CAAoB2C,KAApB,GAA4B,KAAKD,WAAL,CAAiB1C,cAAjB,CAAgC4C,IAAhC,CAAqC,IAArC,EAA2C,IAA3C,CAA5B;AACA,eAAK3C,KAAL,CAAW0C,KAAX,GAAmB,KAAKD,WAAL,CAAiBzC,KAAjB,CAAuB2C,IAAvB,CAA4B,IAA5B,EAAkC,IAAlC,CAAnB;AACA,eAAK1C,KAAL,CAAWyC,KAAX,GAAmB,KAAKD,WAAL,CAAiBxC,KAAjB,CAAuB0C,IAAvB,CAA4B,IAA5B,EAAkC,IAAlC,CAAnB;AACA,eAAKzC,GAAL,CAASwC,KAAT,GAAiB,KAAKD,WAAL,CAAiBvC,GAAjB,CAAqByC,IAArB,CAA0B,IAA1B,EAAgC,IAAhC,CAAjB;AACA,eAAKxC,MAAL,CAAYuC,KAAZ,GAAoB,KAAKD,WAAL,CAAiBtC,MAAjB,CAAwBwC,IAAxB,CAA6B,IAA7B,EAAmC,IAAnC,CAApB;AAEA,eAAKC,SAAL,CAAe,IAAf;AACH,SAtHiE,CAuHlE;AAEA;;;AACOC,QAAAA,WAAW,CAACC,MAAD,EAAkB;AAChC,cAAI,CAAC,KAAKvC,UAAV,EAAsB;AAEtB,eAAKpB,QAAL,CAAcuD,KAAd,GAAsBI,MAAtB;AACA,eAAK3D,QAAL,CAAc4D,MAAd;AACH,SA/HiE,CAiIlE;;;AACOC,QAAAA,KAAK,GAAG;AACX,cAAI,CAAC,KAAKzC,UAAV,EAAsB;AACtB,eAAKM,WAAL,GAAmB,KAAnB;AACA,eAAKoC,YAAL,CAAkBvE,cAAc,CAACgC,IAAjC;AACA,eAAKuB,eAAL,CAAqB,IAArB;;AACA,cAAI,KAAK3B,WAAL,CAAiB6B,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B,iBAAK7B,WAAL,CAAiB8B,OAAjB,CAAyBC,KAAK,IAAIA,KAAK,CAACW,KAAN,EAAlC;AACH;AACJ;;AAEME,QAAAA,SAAS,CAACC,MAAD,EAAoB;AAChC,cAAI,CAAC,KAAK5C,UAAV,EAAsB;AACtB,eAAKW,OAAL,GAAeiC,MAAf;AACH;;AAEMC,QAAAA,SAAS,GAAqB;AACjC,iBAAO,KAAKlC,OAAZ;AACH;;AAESa,QAAAA,gBAAgB,GAAG;AACzB,eAAKsB,KAAL;AAEA,eAAKlE,QAAL,GAAgB,KAAKmE,WAAL,CAAiB3E,YAAY,CAAC4E,QAA9B,EAAwC,KAAxC,CAAhB;AACA,eAAKnD,WAAL,GAAmB,KAAKkD,WAAL,CAAiB3E,YAAY,CAAC6E,WAA9B,EAA2C,CAA3C,CAAnB;AACA,eAAKpE,cAAL,GAAsB,KAAKkE,WAAL,CAAiB3E,YAAY,CAAC8E,cAA9B,EAA8C,IAA9C,CAAtB;AACA,eAAKpE,SAAL,GAAiB,KAAKiE,WAAL,CAAiB3E,YAAY,CAAC+E,SAA9B,EAAyC,IAAzC,CAAjB;AACA,eAAKpE,MAAL,GAAc,KAAKgE,WAAL,CAAiB3E,YAAY,CAACgF,MAA9B,EAAsC,IAAtC,CAAd;AAEA,eAAKpE,YAAL,GAAoB,KAAK+D,WAAL,CAAiB3E,YAAY,CAACiF,YAA9B,EAA4C,CAA5C,CAApB;AACA,eAAKpE,eAAL,GAAuB,KAAK8D,WAAL,CAAiB3E,YAAY,CAACkF,eAA9B,EAA+C,CAA/C,CAAvB;AACA,eAAKpE,YAAL,GAAoB,KAAK6D,WAAL,CAAiB3E,YAAY,CAACmF,YAA9B,EAA4C,CAA5C,CAApB;AACA,eAAKpE,YAAL,GAAoB,KAAK4D,WAAL,CAAiB3E,YAAY,CAACoF,YAA9B,EAA4C,CAA5C,CAApB;AACA,eAAKpE,SAAL,GAAiB,KAAK2D,WAAL,CAAiB3E,YAAY,CAACqF,SAA9B,EAAyC,CAAzC,CAAjB;AACA,eAAKpE,YAAL,GAAoB,KAAK0D,WAAL,CAAiB3E,YAAY,CAACsF,YAA9B,EAA4C,CAA5C,CAApB;AACA,eAAKpE,YAAL,GAAoB,KAAKyD,WAAL,CAAiB3E,YAAY,CAACuF,YAA9B,EAA4C,CAA5C,CAApB;AACA,eAAKpE,eAAL,GAAuB,KAAKwD,WAAL,CAAiB3E,YAAY,CAACwF,eAA9B,EAA+C,CAA/C,CAAvB;AACA,eAAKpE,cAAL,GAAsB,KAAKuD,WAAL,CAAiB3E,YAAY,CAACyF,cAA9B,EAA8C,CAA9C,CAAtB;AACA,eAAKpE,KAAL,GAAa,KAAKsD,WAAL,CAAiB3E,YAAY,CAAC0F,KAA9B,EAAqC,CAArC,CAAb;AACA,eAAKpE,KAAL,GAAa,KAAKqD,WAAL,CAAiB3E,YAAY,CAAC2F,KAA9B,EAAqC,CAArC,CAAb;AACA,eAAKpE,GAAL,GAAW,KAAKoD,WAAL,CAAiB3E,YAAY,CAAC4F,GAA9B,EAAmC,CAAnC,CAAX;AACA,eAAKpE,MAAL,GAAc,KAAKmD,WAAL,CAAiB3E,YAAY,CAAC6F,MAA9B,EAAsC,CAAtC,CAAd,CArByB,CAuBzB;;AACA,eAAKnE,UAAL,GAAkB;AAAA;AAAA,iDAAlB;AAEA,eAAKlB,QAAL,CAAcsF,EAAd,CAAkB/B,KAAD,IAAW;AACxB,gBAAIA,KAAJ,EAAW;AACP,mBAAKO,YAAL,CAAkBvE,cAAc,CAACgC,IAAjC;AACA;AAAA;AAAA,gDAAagE,eAAb,CAA6B,IAA7B;AACH,aAHD,MAGO;AACH;AAAA;AAAA,gDAAaC,gBAAb,CAA8B,IAA9B;AACH;AACJ,WAPD;AAQH;;AAES3C,QAAAA,iBAAiB,GAAG;AAC1B,cAAI,CAAC,KAAKS,WAAN,IAAqB,KAAKA,WAAL,CAAiBmC,cAAjB,CAAgCzC,MAAhC,IAA0C,CAAnE,EAAsE;AAEtE,eAAK7B,WAAL,GAAmB,EAAnB;AACA,cAAIuE,GAAG,GAAG;AAAA;AAAA,uDAAV;AACAA,UAAAA,GAAG,CAACC,OAAJ,GAAc,IAAd;AACAD,UAAAA,GAAG,CAACE,WAAJ,GAAkB;AAAA;AAAA,4CAAaA,WAA/B;;AACA,eAAK,IAAMC,UAAX,IAAyB,KAAKvC,WAAL,CAAiBmC,cAA1C,EAA0D;AACtD;AAAA;AAAA,8CAAaK,uBAAb,CAAqCJ,GAArC,EAA0CG,UAA1C;AACH;AACJ,SAnMiE,CAqMlE;;;AACU/C,QAAAA,eAAe,CAACiD,WAAD,EAA+B;AAAA,cAA9BA,WAA8B;AAA9BA,YAAAA,WAA8B,GAAP,KAAO;AAAA;;AACpD,cAAI,CAAC,KAAKzC,WAAV,EAAuB,OAD6B,CAGpD;AACA;;AACA,eAAKrC,WAAL,CAAiBsC,KAAjB,GAAyB,CAAzB;AACA,eAAKtD,cAAL,CAAoBsD,KAApB,GAA4B,KAAKD,WAAL,CAAiBrD,cAA7C;AACA,eAAKC,SAAL,CAAeqD,KAAf,GAAuB,KAAKD,WAAL,CAAiBpD,SAAxC;AACA,eAAKC,MAAL,CAAYoD,KAAZ,GAAoB,KAAKD,WAAL,CAAiBnD,MAArC;AAEA,eAAKC,YAAL,CAAkBmD,KAAlB,GAA0B,KAAKD,WAAL,CAAiBlD,YAAjB,CAA8BoD,IAA9B,CAAmC,IAAnC,EAAyC,IAAzC,CAA1B;AACA,eAAKnD,eAAL,CAAqBkD,KAArB,GAA6B,KAAKD,WAAL,CAAiBjD,eAAjB,CAAiCmD,IAAjC,CAAsC,IAAtC,EAA4C,IAA5C,CAA7B;AACA,eAAKlD,YAAL,CAAkBiD,KAAlB,GAA0B,KAAKD,WAAL,CAAiBhD,YAAjB,CAA8BkD,IAA9B,CAAmC,IAAnC,EAAyC,IAAzC,CAA1B;AACA,eAAKjD,YAAL,CAAkBgD,KAAlB,GAA0B,KAAKD,WAAL,CAAiB/C,YAAjB,CAA8BiD,IAA9B,CAAmC,IAAnC,EAAyC,IAAzC,CAA1B;AACA,eAAKhD,SAAL,CAAe+C,KAAf,GAAuB,KAAKD,WAAL,CAAiB9C,SAAjB,CAA2BgD,IAA3B,CAAgC,IAAhC,EAAsC,IAAtC,CAAvB;AACA,eAAK/C,YAAL,CAAkB8C,KAAlB,GAA0B,KAAKD,WAAL,CAAiB7C,YAAjB,CAA8B+C,IAA9B,CAAmC,IAAnC,EAAyC,IAAzC,CAA1B;AACA,eAAK9C,YAAL,CAAkB6C,KAAlB,GAA0B,KAAKD,WAAL,CAAiB5C,YAAjB,CAA8B8C,IAA9B,CAAmC,IAAnC,EAAyC,IAAzC,CAA1B;AACA,eAAK7C,eAAL,CAAqB4C,KAArB,GAA6B,KAAKD,WAAL,CAAiB3C,eAAjB,CAAiC6C,IAAjC,CAAsC,IAAtC,EAA4C,IAA5C,CAA7B;AACA,eAAK5C,cAAL,CAAoB2C,KAApB,GAA4B,KAAKD,WAAL,CAAiB1C,cAAjB,CAAgC4C,IAAhC,CAAqC,IAArC,EAA2C,IAA3C,CAA5B;AACA,eAAK3C,KAAL,CAAW0C,KAAX,GAAmB,KAAKD,WAAL,CAAiBzC,KAAjB,CAAuB2C,IAAvB,CAA4B,IAA5B,EAAkC,IAAlC,CAAnB;AACA,eAAK1C,KAAL,CAAWyC,KAAX,GAAmB,KAAKD,WAAL,CAAiBxC,KAAjB,CAAuB0C,IAAvB,CAA4B,IAA5B,EAAkC,IAAlC,CAAnB;AACA,eAAKzC,GAAL,CAASwC,KAAT,GAAiB,KAAKD,WAAL,CAAiBvC,GAAjB,CAAqByC,IAArB,CAA0B,IAA1B,EAAgC,IAAhC,CAAjB;AACA,eAAKxC,MAAL,CAAYuC,KAAZ,GAAoB,KAAKD,WAAL,CAAiBtC,MAAjB,CAAwBwC,IAAxB,CAA6B,IAA7B,EAAmC,IAAnC,CAApB;AAEA,eAAKtC,UAAL,CAAgB8E,aAAhB,CAA8B,KAAKC,UAAnC;AAEA,eAAKxC,SAAL,CAAesC,WAAf;AACH;;AAESG,QAAAA,YAAY,CAACC,IAAD,EAAyB5C,KAAzB,EAAiD;AACnE;AACA;AACA,cAAI4C,IAAI,CAACC,QAAL,CAAc3G,SAAS,CAAC4G,MAAxB,KAAmC,CAAC9C,KAAK,CAAC+C,YAA9C,EAA4D;AACxDH,YAAAA,IAAI,CAAC5C,KAAL,GAAaA,KAAK,CAACC,IAAN,EAAb;AACH;AACJ;AAED;AACJ;AACA;;;AACIM,QAAAA,YAAY,CAAC1B,MAAD,EAAyB;AACjC,cAAI,KAAKd,OAAL,KAAiBc,MAArB,EAA6B;AAE7B,cAAMmE,SAAS,GAAG,KAAKjF,OAAvB;AACA,eAAKA,OAAL,GAAec,MAAf;AACA,eAAKZ,kBAAL,GAA0B,CAA1B;AACA,eAAKG,aAAL,GAAqB,CAArB,CANiC,CAOjC;;AACA,eAAKO,mBAAL,GAA2B,EAA3B;;AAEA,cAAIE,MAAM,KAAK7C,cAAc,CAACiH,OAA9B,EAAuC;AACnC;AACA,iBAAK1D,eAAL;AACH;;AAED,cAAIV,MAAM,KAAK7C,cAAc,CAACgC,IAA9B,EAAoC;AAChC;AACA,gBAAI,KAAKJ,WAAL,CAAiB6B,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B,mBAAK7B,WAAL,CAAiB8B,OAAjB,CAAyBC,KAAK,IAAIA,KAAK,CAACuD,QAAN,EAAlC;AACH;AACJ;;AAED,cAAI,KAAK1G,8BAAL,IAAuC,IAA3C,EAAiD;AAC7C,iBAAKA,8BAAL,CAAoC,IAApC,EAA0CwG,SAA1C,EAAqDnE,MAArD;AACH;AACJ;;AAESsE,QAAAA,gBAAgB,GAAG;AACzB;AACA,eAAK/E,aAAL,GAAqB,KAAKH,kBAAL,GAA0B,KAAKjB,YAAL,CAAkBgD,KAAjE,CAFyB,CAGzB;AAEA;;AACA,eAAK2C,YAAL,CAAkB,KAAK3F,YAAvB,EAAqC,KAAK+C,WAAL,CAAiB/C,YAAtD;AACH;;AAESoG,QAAAA,aAAa,GAAG;AACtB,eAAKjF,WAAL,GAAmB,IAAnB,CADsB,CAEtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACH;;AAESkF,QAAAA,YAAY,GAAG;AACrB,eAAKlF,WAAL,GAAmB,KAAnB,CADqB,CAErB;;AACA,eAAKQ,mBAAL,GAA2B,EAA3B;AACA,eAAK2E,sBAAL;AACH;;AAESC,QAAAA,OAAO,GAAY;AACzB;AACA;AACA,iBAAO,IAAP;AACH;;AAESC,QAAAA,IAAI,GAAS;AACnB;AACA,eAAKb,YAAL,CAAkB,KAAKpF,KAAvB,EAA8B,KAAKwC,WAAL,CAAiBxC,KAA/C;AACA,eAAKoF,YAAL,CAAkB,KAAKnF,GAAvB,EAA4B,KAAKuC,WAAL,CAAiBvC,GAA7C;AACA,eAAKmF,YAAL,CAAkB,KAAKlF,MAAvB,EAA+B,KAAKsC,WAAL,CAAiBtC,MAAhD;AACA,eAAKkF,YAAL,CAAkB,KAAKxF,YAAvB,EAAqC,KAAK4C,WAAL,CAAiB5C,YAAtD;;AAEA,cAAI,KAAKC,eAAL,CAAqB4C,KAArB,GAA6B,CAAjC,EAAoC;AAChC;AACA,iBAAK,IAAIyD,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKtG,YAAL,CAAkB6C,KAAtC,EAA6CyD,CAAC,EAA9C,EAAkD;AAC9C,kBAAMC,UAAU,GAAG,KAAKzF,kBAAL,GAA2B,KAAKb,eAAL,CAAqB4C,KAArB,GAA6ByD,CAA3E;;AACA,mBAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKpG,KAAL,CAAWyC,KAA/B,EAAsC2D,CAAC,EAAvC,EAA2C;AACvC,qBAAKhF,mBAAL,CAAyBiF,IAAzB,CAA8B;AAC1BC,kBAAAA,KAAK,EAAEF,CADmB;AAE1BG,kBAAAA,YAAY,EAAEL,CAFY;AAG1BC,kBAAAA,UAAU,EAAEA;AAHc,iBAA9B;AAKH;;AACD,mBAAKf,YAAL,CAAkB,KAAKvF,eAAvB,EAAwC,KAAK2C,WAAL,CAAiB3C,eAAzD;AACH;AACJ,WAbD,MAcK;AACD;AACA,iBAAK,IAAIuG,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAG,KAAKpG,KAAL,CAAWyC,KAA/B,EAAsC2D,EAAC,EAAvC,EAA2C;AACvC,mBAAK,IAAIF,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAG,KAAKtG,YAAL,CAAkB6C,KAAtC,EAA6CyD,EAAC,EAA9C,EAAkD;AAC9C,qBAAKM,UAAL,CAAgBJ,EAAhB,EAAmBF,EAAnB;AACH;AACJ;AACJ;AACJ;;AAESO,QAAAA,mBAAmB,GAAS;AAClC;AACA,iBAAO,KAAKrF,mBAAL,CAAyBc,MAAzB,GAAkC,CAAzC,EAA4C;AACxC,gBAAMwE,UAAU,GAAG,KAAKtF,mBAAL,CAAyB,CAAzB,CAAnB,CADwC,CAGxC;;AACA,gBAAI,KAAKV,kBAAL,IAA2BgG,UAAU,CAACP,UAA1C,EAAsD;AAClD;AACA,mBAAK/E,mBAAL,CAAyBuF,KAAzB;;AACA,mBAAKH,UAAL,CAAgBE,UAAU,CAACJ,KAA3B,EAAkCI,UAAU,CAACH,YAA7C;AACH,aAJD,MAIO;AACH;AACA;AACH;AACJ;AACJ;;AAESK,QAAAA,OAAO,GAAY;AACzB,cAAI,KAAKZ,OAAL,EAAJ,EAAoB;AAChB,iBAAKC,IAAL;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;;AAESO,QAAAA,UAAU,CAACF,KAAD,EAAgBC,YAAhB,EAAsC;AACtD,cAAMM,SAAS,GAAG,KAAKC,iBAAL,CAAuBR,KAAvB,CAAlB;AACA,cAAMS,QAAQ,GAAG,KAAKC,gBAAL,CAAsBV,KAAtB,EAA6BC,YAA7B,CAAjB;AACA,eAAKU,YAAL,CAAkBJ,SAAlB,EAA6BE,QAA7B;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACID,QAAAA,iBAAiB,CAACR,KAAD,EAA0C;AACvD;AACA,eAAKlB,YAAL,CAAkB,KAAKrF,KAAvB,EAA8B,KAAKyC,WAAL,CAAiBzC,KAA/C,EAFuD,CAGvD;;AACA,cAAImH,WAAW,GAAG,CAAlB,CAJuD,CAKvD;AACA;;AACA,cAAI,KAAKjH,GAAL,CAASwC,KAAT,IAAkB,GAAlB,IAAyB,KAAKzC,KAAL,CAAWyC,KAAX,GAAmB,CAAhD,EAAmD;AAC/CyE,YAAAA,WAAW,GAAI,KAAKjH,GAAL,CAASwC,KAAT,GAAiB,KAAKzC,KAAL,CAAWyC,KAA7B,GAAsC6D,KAApD;AACH,WAFD,MAEO,IAAI,KAAKtG,KAAL,CAAWyC,KAAX,GAAmB,CAAvB,EAA0B;AAC7ByE,YAAAA,WAAW,GAAI,KAAKjH,GAAL,CAASwC,KAAT,IAAkB,KAAKzC,KAAL,CAAWyC,KAAX,GAAmB,CAArC,CAAD,GAA4C6D,KAA5C,GAAoD,KAAKrG,GAAL,CAASwC,KAAT,GAAiB,CAAnF;AACH;;AACD,cAAM0E,MAAM,GAAG5I,gBAAgB,CAAC,KAAKwB,KAAL,CAAW0C,KAAX,GAAmByE,WAApB,CAA/B;AAEA,iBAAO;AACHE,YAAAA,CAAC,EAAEC,IAAI,CAACC,GAAL,CAASH,MAAT,CADA;AAEHI,YAAAA,CAAC,EAAEF,IAAI,CAACG,GAAL,CAASL,MAAT;AAFA,WAAP;AAIH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIH,QAAAA,gBAAgB,CAACV,KAAD,EAAgBC,YAAhB,EAAgE;AAC5E;AACA;AACA,cAAMkB,cAAc,GAAG,CAAClB,YAAD,EAAuB3G,YAAvB,EAA6CE,cAA7C,KAAwE;AAC3F,gBAAIF,YAAY,IAAI,CAAhB,IAAqBE,cAAc,KAAK,CAA5C,EAA+C,OAAO,CAAP;AAC/C,gBAAM4H,QAAQ,GAAG5H,cAAc,IAAIF,YAAY,GAAG,CAAnB,CAA/B,CAF2F,CAG3F;;AAEA,gBAAIA,YAAY,GAAG,CAAf,KAAqB,CAAzB,EAA4B;AACxB;AACA,kBAAI2G,YAAY,KAAK,CAArB,EAAwB,OAAO,CAAP;;AACxB,kBAAIA,YAAY,GAAG,CAAf,KAAqB,CAAzB,EAA4B;AACxB;AACA,oBAAMoB,eAAe,GAAGN,IAAI,CAACO,KAAL,CAAWrB,YAAY,GAAG,CAA1B,CAAxB;AACA,uBAAO,CAACoB,eAAD,GAAmBD,QAA1B;AACH,eAJD,MAKK;AACD;AACA,oBAAMC,gBAAe,GAAGN,IAAI,CAACQ,IAAL,CAAUtB,YAAY,GAAG,CAAzB,CAAxB;;AACA,uBAAOoB,gBAAe,GAAGD,QAAzB;AACH;AACJ,aAbD,MAaO;AACH;AACA,kBAAInB,YAAY,KAAK,CAArB,EAAwB,OAAO,CAACmB,QAAD,GAAY,CAAnB;;AACxB,kBAAInB,YAAY,GAAG,CAAf,KAAqB,CAAzB,EAA4B;AACxB;AACA,oBAAMoB,iBAAe,GAAGN,IAAI,CAACO,KAAL,CAAWrB,YAAY,GAAG,CAA1B,CAAxB;;AACA,uBAAO,CAACmB,QAAD,GAAY,CAAZ,GAAgBC,iBAAe,GAAGD,QAAzC;AACH,eAJD,MAKK;AACD;AACA,oBAAMC,iBAAe,GAAGN,IAAI,CAACO,KAAL,CAAWrB,YAAY,GAAG,CAA1B,CAAxB;;AACA,uBAAOmB,QAAQ,GAAG,CAAX,GAAeC,iBAAe,GAAGD,QAAxC;AACH;AACJ;AACJ,WAhCD;;AAkCA,eAAKtC,YAAL,CAAkB,KAAKtF,cAAvB,EAAuC,KAAK0C,WAAL,CAAiB1C,cAAxD;AACA,cAAMA,cAAc,GAAG2H,cAAc,CAAClB,YAAD,EAAe,KAAK3G,YAAL,CAAkB6C,KAAjC,EAAwC,KAAK3C,cAAL,CAAoB2C,KAA5D,CAArC;;AACA,cAAI,KAAKvC,MAAL,CAAYuC,KAAZ,IAAqB,CAAzB,EAA4B;AACxB,mBAAO;AAAE2E,cAAAA,CAAC,EAAEtH,cAAL;AAAqByH,cAAAA,CAAC,EAAE;AAAxB,aAAP;AACH;;AAED,cAAMV,SAAS,GAAG,KAAKC,iBAAL,CAAuBR,KAAvB,CAAlB,CA3C4E,CA4C5E;;AACA,cAAMwB,aAAa,GAAG;AAAEV,YAAAA,CAAC,EAAE,CAACP,SAAS,CAACU,CAAhB;AAAmBA,YAAAA,CAAC,EAAEV,SAAS,CAACO;AAAhC,WAAtB;;AACA,cAAI,KAAKlH,MAAL,CAAYuC,KAAZ,IAAqB,CAAzB,EAA4B;AACxB,mBAAO;AACH2E,cAAAA,CAAC,EAAEU,aAAa,CAACV,CAAd,GAAkBtH,cADlB;AAEHyH,cAAAA,CAAC,EAAEO,aAAa,CAACP,CAAd,GAAkBzH;AAFlB,aAAP;AAIH;;AAED,iBAAO;AACHsH,YAAAA,CAAC,EAAEP,SAAS,CAACO,CAAV,GAAc,KAAKlH,MAAL,CAAYuC,KAA1B,GAAkCqF,aAAa,CAACV,CAAd,GAAkBtH,cADpD;AAEHyH,YAAAA,CAAC,EAAEV,SAAS,CAACU,CAAV,GAAc,KAAKrH,MAAL,CAAYuC,KAA1B,GAAkCqF,aAAa,CAACP,CAAd,GAAkBzH;AAFpD,WAAP;AAIH;;AAEDmH,QAAAA,YAAY,CAACJ,SAAD,EAAsCE,QAAtC,EAAgF;AACxF,cAAI,CAAC,KAAKjG,aAAV,EAAyB;AACrB,iBAAKA,aAAL,GAAqB,KAAKqE,UAAL,CAAgB4C,MAArC;;AACA,gBAAI,CAAC,KAAKjH,aAAV,EAAyB;AACrB,kBAAIxD,MAAJ,EAAY;AACR,qBAAK0K,oBAAL,CAA0BnB,SAA1B,EAAqCE,QAArC;AACH;;AACD;AACH;AACJ;;AAED,cAAMkB,MAAM,GAAG,KAAKC,iBAAL,EAAf;AACA,cAAI,CAACD,MAAL,EAAa;AAEb;AAAA;AAAA,4CAAaE,cAAb,CAA4B,IAA5B,EAAkCF,MAAlC,EAdwF,CAexF;;AACA,cAAMG,UAAU,GAAG,KAAKC,IAAL,CAAUC,gBAAV,EAAnB;AACAL,UAAAA,MAAM,CAACI,IAAP,CAAYE,gBAAZ,CACIH,UAAU,CAAChB,CAAX,GAAeL,QAAQ,CAACK,CAD5B,EAEIgB,UAAU,CAACb,CAAX,GAAeR,QAAQ,CAACQ,CAF5B,EAGIa,UAAU,CAACI,CAHf;AAKAP,UAAAA,MAAM,CAAC5C,IAAP,CAAYoD,UAAZ,CAAuBhG,KAAvB,GAA+BjE,gBAAgB,CAAC6I,IAAI,CAACqB,KAAL,CAAW7B,SAAS,CAACU,CAArB,EAAwBV,SAAS,CAACO,CAAlC,CAAD,CAA/C;AACAa,UAAAA,MAAM,CAAC5C,IAAP,CAAYsD,KAAZ,CAAkBlG,KAAlB,IAA2B,KAAK/C,SAAL,CAAe+C,KAA1C,CAvBwF,CAwBxF;AACA;;AACAwF,UAAAA,MAAM,CAACW,OAAP;;AAEA,cAAI,KAAK5J,uBAAL,IAAgC,IAApC,EAA0C;AACtC,iBAAKA,uBAAL,CAA6BiJ,MAA7B;AACH;AACJ;;AAEeD,QAAAA,oBAAoB,CAACnB,SAAD,EAAsCE,QAAtC,EAA0E;AAAA;;AAAA;AAC1G;AACA,gBAAM8B,UAAU,GAAG,sDAAnB,CAF0G,CAG1G;;AACAC,YAAAA,MAAM,CAACC,OAAP,CAAeC,OAAf,CAAuB,UAAvB,EAAmC,YAAnC,EAAiDH,UAAjD,EACKI,IADL,CACWC,IAAD,IAAU;AACZ,kBAAI,CAACA,IAAL,EAAW;AAEX9L,cAAAA,YAAY,CAAC+L,OAAb,CAAqB;AAAED,gBAAAA,IAAI,EAAEA;AAAR,eAArB,EAAqC,CAACE,GAAD,EAAMrB,MAAN,KAAiB;AAClD,oBAAIqB,GAAJ,EAAS;AACLC,kBAAAA,OAAO,CAACC,KAAR,CAAcF,GAAd;AACA;AACH;;AACD,gBAAA,KAAI,CAACtI,aAAL,GAAqBiH,MAArB;;AACA,oBAAME,MAAM,GAAG,KAAI,CAACC,iBAAL,EAAf;;AACA,oBAAI,CAACD,MAAL,EAAa;AAEb;AAAA;AAAA,kDAAaE,cAAb,CAA4B,KAA5B,EAAkCF,MAAlC,EATkD,CAUlD;;AACA,oBAAMG,UAAU,GAAG,KAAI,CAACC,IAAL,CAAUC,gBAAV,EAAnB;;AACAL,gBAAAA,MAAM,CAACI,IAAP,CAAYE,gBAAZ,CACIH,UAAU,CAAChB,CAAX,GAAeL,QAAQ,CAACK,CAD5B,EAEIgB,UAAU,CAACb,CAAX,GAAeR,QAAQ,CAACQ,CAF5B,EAGIa,UAAU,CAACI,CAHf;AAKAP,gBAAAA,MAAM,CAAC5C,IAAP,CAAYoD,UAAZ,CAAuBhG,KAAvB,GAA+BjE,gBAAgB,CAAC6I,IAAI,CAACqB,KAAL,CAAW7B,SAAS,CAACU,CAArB,EAAwBV,SAAS,CAACO,CAAlC,CAAD,CAA/C;AACAa,gBAAAA,MAAM,CAAC5C,IAAP,CAAYsD,KAAZ,CAAkBlG,KAAlB,IAA2B,KAAI,CAAC/C,SAAL,CAAe+C,KAA1C;AACAwF,gBAAAA,MAAM,CAACW,OAAP;AACH,eApBD;AAqBH,aAzBL;AAJ0G;AA8B7G;;AAESV,QAAAA,iBAAiB,GAAkB;AACzC,cAAMqB,UAAU,GAAG;AAAA;AAAA,wCAAWC,OAAX,CAAmB;AAAA;AAAA,4CAAaC,YAAhC,EAA8C,KAAK3I,aAAnD,CAAnB;;AACA,cAAI,CAACyI,UAAL,EAAiB;AACbF,YAAAA,OAAO,CAACC,KAAR,CAAc,8CAAd;AACA,mBAAO,IAAP;AACH,WALwC,CAOzC;;;AACA,cAAMrB,MAAM,GAAGsB,UAAU,CAACG,YAAX;AAAA;AAAA,+BAAf;;AACA,cAAI,CAACzB,MAAL,EAAa;AACToB,YAAAA,OAAO,CAACC,KAAR,CAAc,uDAAd;AACAC,YAAAA,UAAU,CAACI,OAAX;AACA,mBAAO,IAAP;AACH;;AAED,cAAIrM,MAAJ,EAAY;AACRiM,YAAAA,UAAU,CAACK,IAAX,GAAkBhL,OAAO,CAACiL,mBAA1B;AACH;;AAED,iBAAO5B,MAAP;AACH;;AAESrG,QAAAA,cAAc,CAACH,SAAD,EAAoB;AACxC,cAAIA,SAAS,GAAG,CAAZ,IAAiB;AAAA;AAAA,8BAAMqI,WAAN,EAAjB,IAAwC;AAAA;AAAA,8BAAMC,QAAlD,EAA4D;AACxD,iBAAK7I,cAAL,GAAsB;AAAA;AAAA,gCAAM8I,WAAN,CAAkBC,YAAlB,CAA+BC,GAA/B,CAAmCzI,SAAnC,CAAtB;;AACA,gBAAI,CAAC,KAAKP,cAAV,EAA0B;AACtB;AAAA;AAAA,sCAAQ,SAAR,oDAAmEO,SAAnE;AACH,aAJuD,CAKxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACH,WAfuC,CAgBxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACH;;AAED0I,QAAAA,UAAU,CAACpC,MAAD,EAAiBhB,QAAjB,EAAiCqD,QAAjC,EAAiDC,QAAjD,EAAmE;AACzE,cAAI,CAACtC,MAAL,EAAa;AAEb,cAAMuC,UAAU,GAAG;AAAA;AAAA,wCAAWd,OAAX,CAAmB,KAAKnB,IAAxB,EAA8BN,MAA9B,CAAnB;AACA,cAAI,CAACuC,UAAL,EAAiB;AAEjBA,UAAAA,UAAU,CAAC/B,gBAAX,CAA4BxB,QAA5B;AACAuD,UAAAA,UAAU,CAACC,gBAAX,CAA4BH,QAA5B,EAPyE,CAQzE;AACA;;AACA,eAAKI,YAAL,CAAkB,MAAM;AACpB;AAAA;AAAA,0CAAWC,UAAX,CAAsBH,UAAtB;AACH,WAFD,EAEGD,QAFH;AAGH;AAED;AACJ;AACA;;;AACcK,QAAAA,UAAU,GAAY;AAC5B,cAAI,CAAC,KAAKvL,cAAL,CAAoBsD,KAAzB,EAAgC,OAAO,IAAP;;AAEhC,cAAI,KAAKxB,OAAT,EAAkB;AACd,mBAAO,KAAKA,OAAL,CAAayJ,UAAb,EAAP;AACH;;AAED,iBAAO;AAAA;AAAA,sCAAUC,mBAAV,CAA8B,KAAKtC,IAAL,CAAUtB,QAAxC,CAAP;AACH;;AAEM6D,QAAAA,IAAI,CAACC,SAAD,EAA0B;AACjC,cAAI,CAAC,KAAK3L,QAAN,IAAkB,CAAC,KAAKA,QAAL,CAAcuD,KAArC,EAA4C;AACxC;AACH;;AAED,cAAI,CAAC,KAAKiI,UAAL,EAAL,EAAwB;AACpB;AACH;;AAED,kBAAQ,KAAKlK,OAAb;AACI,iBAAK/B,cAAc,CAACgC,IAApB;AACI,mBAAKqK,gBAAL;AACA;;AACJ,iBAAKrM,cAAc,CAACiH,OAApB;AACI,mBAAKqF,mBAAL;AACA;;AACJ,iBAAKtM,cAAc,CAACuM,QAApB;AACI,mBAAKC,oBAAL;AACA;;AACJ,iBAAKxM,cAAc,CAACyM,cAApB;AACI,mBAAKC,0BAAL;AACA;;AACJ,iBAAK1M,cAAc,CAAC2M,SAApB;AACI,mBAAKC,qBAAL;AACA;;AACJ;AACI;AAjBR;;AAoBA,eAAKlL,WAAL,CAAiBsC,KAAjB,IAA0BoI,SAA1B;AACA,eAAKnK,kBAAL,IAA2BmK,SAA3B;AACA,eAAKlK,iBAAL,IAA0BkK,SAA1B;AAEA,eAAKlI,SAAL;AACH;;AAESmI,QAAAA,gBAAgB,GAAG;AACzB,cAAI,KAAKpK,kBAAL,IAA2B,KAAKpB,YAAL,CAAkBmD,KAAjD,EAAwD;AACpD,iBAAKO,YAAL,CAAkBvE,cAAc,CAACiH,OAAjC;AACH;AACJ;;AAESqF,QAAAA,mBAAmB,GAAG;AAC5B,cAAI,CAAC,KAAK3L,SAAL,CAAeqD,KAApB,EACI,KAAKO,YAAL,CAAkBvE,cAAc,CAACuM,QAAjC,EADJ,KAEK;AACD,gBAAI,KAAKtK,kBAAL,IAA2B,KAAKnB,eAAL,CAAqBkD,KAApD,EAA2D;AACvD,mBAAKO,YAAL,CAAkBvE,cAAc,CAACuM,QAAjC;AACH;AACJ;AACJ;;AAESC,QAAAA,oBAAoB,GAAG;AAC7B,cAAI,KAAKvK,kBAAL,GAA0B,KAAKlB,YAAL,CAAkBiD,KAAhD,EAAuD;AACnD,iBAAKqD,YAAL;AACA,gBAAI,KAAKzG,MAAL,CAAYoD,KAAhB,EACI,KAAKO,YAAL,CAAkBvE,cAAc,CAACyM,cAAjC,EADJ,KAGI,KAAKlI,YAAL,CAAkBvE,cAAc,CAAC2M,SAAjC;AACJ;AACH,WAR4B,CAU7B;;;AACA,cAAI,CAAC,KAAKxK,WAAV,EAAuB;AACnB,iBAAKiF,aAAL;AACH,WAFD,MAGK,IAAI,KAAKnF,kBAAL,IAA2B,KAAKG,aAApC,EAAmD;AACpD,iBAAK+F,OAAL;;AACA,gBAAI,KAAK/G,eAAL,CAAqB4C,KAArB,IAA8B,CAAlC,EAAqC;AACjC,mBAAKmD,gBAAL;AACH,aAFD,MAGK;AACD;AACA,mBAAK/E,aAAL,GAAqB,KAAKH,kBAAL,GAA0B,QAA/C;AACH;AACJ;;AAED,cAAI4K,WAAW,GAAG,KAAKlK,mBAAL,CAAyBc,MAAzB,GAAkC,CAApD,CAzB6B,CA0B7B;;AACA,eAAKuE,mBAAL;;AACA,cAAI6E,WAAW,IAAI,KAAKlK,mBAAL,CAAyBc,MAAzB,IAAmC,CAAtD,EAAyD;AACrD,iBAAK0D,gBAAL;AACH;AACJ;;AAESuF,QAAAA,0BAA0B,GAAG;AACnC,cAAI,KAAKzK,kBAAL,IAA2B,KAAKf,YAAL,CAAkB8C,KAAjD,EAAwD;AACpD,iBAAKO,YAAL,CAAkBvE,cAAc,CAACiH,OAAjC;AACH;AACJ;;AAES2F,QAAAA,qBAAqB,GAAG;AAC9B;AACA,eAAKnM,QAAL,CAAcuD,KAAd,GAAsB,KAAtB;AACA,eAAKvD,QAAL,CAAc4D,MAAd;AACH;;AA/rBiE,O,UAE3D+G,mB,GAA8B,U;;;;;iBAGf,E;;;;;;;iBAKc;AAAA;AAAA,2C;;;;;;;iBAGF;AAAA;AAAA,yC", "sourcesContent": ["import { _decorator, assetManager, misc, Prefab, Quat, Vec3, Enum, CCString } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nimport { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\r\nimport { ResEmitter } from 'db://assets/bundles/common/script/autogen/luban/schema';\r\nimport { LubanMgr } from 'db://assets/bundles/common/script/luban/LubanMgr';\r\nimport Entity from 'db://assets/bundles/common/script/game/ui/base/Entity';\r\nimport { GameConst } from 'db://assets/scripts/core/base/GameConst';\r\nimport { BulletData } from '../data/bullet/BulletData';\r\nimport { EmitterData } from '../data/bullet/EmitterData';\r\nimport { Bullet, BulletProperty } from './Bullet';\r\nimport { BulletSystem } from './BulletSystem';\r\nimport { EventGroup, EventGroupContext } from \"./EventGroup\";\r\nimport { ObjectPool } from './ObjectPool';\r\nimport { Property, PropertyContainerComponent } from './PropertyContainer';\r\nimport type PlaneBase from '../ui/plane/PlaneBase';\r\nimport { ExpressionValue } from '../data/bullet/ExpressionValue';\r\nimport { logWarn } from 'db://assets/scripts/utils/Logger';\r\n\r\n// // 这个import仅用于编辑功能\r\n// import { BulletEnum } from 'db://assets/editor/enum-gen/BulletEnum'\r\n\r\nconst { ccclass, executeInEditMode, property, disallowMultiple, menu } = _decorator;\r\nconst { degreesToRadians, radiansToDegrees } = misc;\r\n\r\n/**\r\n * 发射器状态变换\r\n * [None] -> <InitialDelay> -> [Prewarm] -> <PrewarmDuration> -> [Emitting]\r\n *                                 ^                                 |\r\n *                                 |                                 v\r\n *                                 |                           <EmitDuration>\r\n *                                 |                                 |\r\n *                                 |                              isLoop? ---no---> [Completed] \r\n *                                 |                                 |\r\n *                                 |                                 |yes\r\n *                                 |                                 |\r\n *                                 |                                 v\r\n *                                 -------<LoopInterval>------[LoopEndReached]\r\n */\r\nexport enum eEmitterStatus {\r\n    None, Prewarm, Emitting, LoopEndReached, Completed\r\n}\r\n\r\n// 用枚举定义属性\r\nexport enum eEmitterProp {\r\n    IsActive = 1, IsOnlyInScreen, IsPreWarm, IsLoop,\r\n    InitialDelay, PrewarmDuration, EmitDuration, EmitInterval, EmitPower, LoopInterval,\r\n    PerEmitCount, PerEmitInterval, PerEmitOffsetX,\r\n    Angle, Count, Arc, Radius,\r\n    ElapsedTime,\r\n}\r\n\r\n/**\r\n * 说明：\r\n * 因为发射器属性可能需要从emitterData里的公式计算，如: randi(0,360);\r\n * 但同时也可能被事件组修改，参考: EmitterEventActions\r\n * 事件组的优先级要高于emitterData的公式计算, 因此, 如果一个属性带了ePropMask.EventGroup标记, 后续ReEval时就直接跳过\r\n */\r\nexport enum ePropMask {\r\n    ReEval = 1 << 0, // 需要重新从公式计算\r\n    EventGroup = 1 << 1, // 需要被事件组修改\r\n}\r\n\r\nexport type onBulletCreatedDelegate = (bullet: Bullet) => void;\r\nexport type onEmitterStatusChangedDelegate = (emitter: Emitter, oldStatus: eEmitterStatus, newStatus: eEmitterStatus) => void;\r\n\r\n/**\r\n * 目前Emitter,EventGroup,BulletSystem的状态管理还是比较混乱\r\n * 需要看下怎么调整，使代码不论是运行时，还是编辑器下，都更加健壮\r\n * - young\r\n */\r\n@ccclass('Emitter')\r\n// @inspector('editor/inspector/components/emitter')\r\n@menu('子弹系统/发射器')\r\n@executeInEditMode(true)\r\n@disallowMultiple(true)\r\nexport class Emitter extends PropertyContainerComponent<eEmitterProp> {\r\n\r\n    static kBulletNameInEditor: string = \"_bullet_\";\r\n\r\n    @property({displayName: '名称', editorOnly: true})\r\n    emitterName: string = '';                // 备注(策划用)\r\n\r\n    // @property({ type: Enum(BulletEnum), displayName: \"子弹ID\" })\r\n    // readonly bulletID: number = 0;\r\n    @property({ type: EmitterData, displayName: \"发射器属性\" })\r\n    readonly emitterData: EmitterData = new EmitterData();\r\n\r\n    @property({ type: BulletData, displayName: \"子弹属性\" })\r\n    readonly bulletData: BulletData = new BulletData();\r\n\r\n    // callbacks\r\n    onBulletCreatedCallback: onBulletCreatedDelegate | null = null;\r\n    onEmitterStatusChangedCallback: onEmitterStatusChangedDelegate | null = null;\r\n\r\n    // 以下属性缓存为了性能优化(减少this.getProperty<T>的调用)\r\n    public isActive!: Property<boolean>;\r\n    public isOnlyInScreen!: Property<boolean>;\r\n    public isPreWarm!: Property<boolean>;\r\n    public isLoop!: Property<boolean>;\r\n    public initialDelay!: Property<number>;\r\n    public preWarmDuration!: Property<number>;\r\n    public emitDuration!: Property<number>;\r\n    public emitInterval!: Property<number>;\r\n    public emitPower!: Property<number>;\r\n    public loopInterval!: Property<number>;\r\n    public perEmitCount!: Property<number>;\r\n    public perEmitInterval!: Property<number>;\r\n    public perEmitOffsetX!: Property<number>;\r\n    public angle!: Property<number>;\r\n    public count!: Property<number>;\r\n    public arc!: Property<number>;\r\n    public radius!: Property<number>;\r\n    public elapsedTime!: Property<number>; \r\n    // 以下用于事件组修改子弹的属性，（不直接修改bulletData)\r\n    public bulletProp!: BulletProperty;\r\n\r\n    // 发射器自己的事件组\r\n    public eventGroups: EventGroup[] = [];\r\n\r\n    // 私有变量\r\n    protected _hasAwaken: boolean = false;\r\n    protected _emitterId: number = 0;\r\n    protected _status: eEmitterStatus = eEmitterStatus.None;\r\n    protected _statusElapsedTime: number = 0;\r\n    protected _totalElapsedTime: number = 0;\r\n    protected _isEmitting: boolean = false;\r\n    protected _nextEmitTime: number = 0;\r\n\r\n    protected _bulletPrefab: Prefab | null = null;\r\n    protected _prewarmEffectPrefab: Prefab | null = null;\r\n    protected _emitEffectPrefab: Prefab | null = null;\r\n    protected _entity: PlaneBase | null = null;\r\n    protected _emitterConfig: ResEmitter | undefined = undefined;\r\n    \r\n    // Per-emit timing tracking\r\n    protected _perEmitBulletQueue: Array<{ index: number, perEmitIndex: number, targetTime: number }> = [];\r\n\r\n    get isEmitting(): boolean { return this._isEmitting; }\r\n    get status(): eEmitterStatus { return this._status; }\r\n    get statusElapsedTime(): number { return this._statusElapsedTime; }\r\n    get totalElapsedTime(): number { return this._totalElapsedTime; }\r\n    get emitterId(): number { return this._emitterId; }\r\n    get config(): ResEmitter | undefined { return this._emitterConfig; }\r\n    set emitterId(id: number) {\r\n        this._emitterId = id;\r\n        this.loadConfigByID(id);\r\n    }\r\n\r\n    protected onLoad(): void {\r\n        this.createProperties();\r\n        this.createEventGroups();\r\n\r\n        // 更新属性\r\n        this.resetProperties(true);\r\n        this._hasAwaken = true;\r\n    }\r\n\r\n    protected onDestroy(): void {\r\n        this._hasAwaken = false;\r\n        if (this.eventGroups.length > 0) {\r\n            this.eventGroups.forEach(group => group.tryStop());\r\n        }\r\n    }\r\n\r\n    //#region \"Editor Region\"\r\n    public onLostFocusInEditor(): void {\r\n        this.updatePropertiesInEditor();\r\n        this.createEventGroups();\r\n    }\r\n\r\n    public updatePropertiesInEditor() {\r\n        if (!this.emitterData) return;\r\n\r\n        this.isActive.value = true;\r\n        this.isOnlyInScreen.value = this.emitterData.isOnlyInScreen;\r\n        this.isPreWarm.value = this.emitterData.isPreWarm;\r\n        this.isLoop.value = this.emitterData.isLoop;\r\n\r\n        this.initialDelay.value = this.emitterData.initialDelay.eval(null, true);\r\n        this.preWarmDuration.value = this.emitterData.preWarmDuration.eval(null, true);\r\n        this.emitDuration.value = this.emitterData.emitDuration.eval(null, true);\r\n        this.emitInterval.value = this.emitterData.emitInterval.eval(null, true);\r\n        this.emitPower.value = this.emitterData.emitPower.eval(null, true);\r\n        this.loopInterval.value = this.emitterData.loopInterval.eval(null, true);\r\n        this.perEmitCount.value = this.emitterData.perEmitCount.eval(null, true);\r\n        this.perEmitInterval.value = this.emitterData.perEmitInterval.eval(null, true);\r\n        this.perEmitOffsetX.value = this.emitterData.perEmitOffsetX.eval(null, true);\r\n        this.angle.value = this.emitterData.angle.eval(null, true);\r\n        this.count.value = this.emitterData.count.eval(null, true);\r\n        this.arc.value = this.emitterData.arc.eval(null, true);\r\n        this.radius.value = this.emitterData.radius.eval(null, true);\r\n\r\n        this.notifyAll(true);\r\n    }\r\n    //#endregion \"Editor Region\"\r\n\r\n    // 通过这个接口来启用和禁用发射器\r\n    public setIsActive(active: boolean) {\r\n        if (!this._hasAwaken) return;\r\n        \r\n        this.isActive.value = active;\r\n        this.isActive.notify();\r\n    }\r\n\r\n    // 这个接口清理发射器的状态，全部从头开始\r\n    public reset() {\r\n        if (!this._hasAwaken) return;\r\n        this._isEmitting = false;\r\n        this.changeStatus(eEmitterStatus.None);\r\n        this.resetProperties(true);\r\n        if (this.eventGroups.length > 0) {\r\n            this.eventGroups.forEach(group => group.reset());\r\n        }\r\n    }\r\n\r\n    public setEntity(entity: PlaneBase) {\r\n        if (!this._hasAwaken) return;\r\n        this._entity = entity;\r\n    }\r\n\r\n    public getEntity(): PlaneBase | null {\r\n        return this._entity;\r\n    }\r\n\r\n    protected createProperties() {\r\n        this.clear();\r\n\r\n        this.isActive = this.addProperty(eEmitterProp.IsActive, false);\r\n        this.elapsedTime = this.addProperty(eEmitterProp.ElapsedTime, 0);\r\n        this.isOnlyInScreen = this.addProperty(eEmitterProp.IsOnlyInScreen, true);\r\n        this.isPreWarm = this.addProperty(eEmitterProp.IsPreWarm, true);\r\n        this.isLoop = this.addProperty(eEmitterProp.IsLoop, true);\r\n\r\n        this.initialDelay = this.addProperty(eEmitterProp.InitialDelay, 0);\r\n        this.preWarmDuration = this.addProperty(eEmitterProp.PrewarmDuration, 0);\r\n        this.emitDuration = this.addProperty(eEmitterProp.EmitDuration, 0);\r\n        this.emitInterval = this.addProperty(eEmitterProp.EmitInterval, 0);\r\n        this.emitPower = this.addProperty(eEmitterProp.EmitPower, 1);\r\n        this.loopInterval = this.addProperty(eEmitterProp.LoopInterval, 0);\r\n        this.perEmitCount = this.addProperty(eEmitterProp.PerEmitCount, 1);\r\n        this.perEmitInterval = this.addProperty(eEmitterProp.PerEmitInterval, 0);\r\n        this.perEmitOffsetX = this.addProperty(eEmitterProp.PerEmitOffsetX, 0);\r\n        this.angle = this.addProperty(eEmitterProp.Angle, 0);\r\n        this.count = this.addProperty(eEmitterProp.Count, 1);\r\n        this.arc = this.addProperty(eEmitterProp.Arc, 0);\r\n        this.radius = this.addProperty(eEmitterProp.Radius, 0);\r\n\r\n        // 子弹相关属性\r\n        this.bulletProp = new BulletProperty();\r\n\r\n        this.isActive.on((value) => {\r\n            if (value) {\r\n                this.changeStatus(eEmitterStatus.None);\r\n                BulletSystem.onCreateEmitter(this);\r\n            } else {\r\n                BulletSystem.onDestroyEmitter(this);\r\n            }\r\n        });\r\n    }\r\n\r\n    protected createEventGroups() {\r\n        if (!this.emitterData || this.emitterData.eventGroupData.length <= 0) return;\r\n\r\n        this.eventGroups = [];\r\n        let ctx = new EventGroupContext();\r\n        ctx.emitter = this;\r\n        ctx.playerPlane = BulletSystem.playerPlane;\r\n        for (const eventGroup of this.emitterData.eventGroupData) {\r\n            BulletSystem.createEmitterEventGroup(ctx, eventGroup);\r\n        }\r\n    }\r\n\r\n    // reset properties from emitterData\r\n    protected resetProperties(forceNotify: boolean = false) {\r\n        if (!this.emitterData) return;\r\n\r\n        // 因为循环结束要重置属性，但不希望把isActive给重置了\r\n        // this.isActive.value = false;\r\n        this.elapsedTime.value = 0;\r\n        this.isOnlyInScreen.value = this.emitterData.isOnlyInScreen;\r\n        this.isPreWarm.value = this.emitterData.isPreWarm;\r\n        this.isLoop.value = this.emitterData.isLoop;\r\n\r\n        this.initialDelay.value = this.emitterData.initialDelay.eval(null, true);\r\n        this.preWarmDuration.value = this.emitterData.preWarmDuration.eval(null, true);\r\n        this.emitDuration.value = this.emitterData.emitDuration.eval(null, true);\r\n        this.emitInterval.value = this.emitterData.emitInterval.eval(null, true);\r\n        this.emitPower.value = this.emitterData.emitPower.eval(null, true);\r\n        this.loopInterval.value = this.emitterData.loopInterval.eval(null, true);\r\n        this.perEmitCount.value = this.emitterData.perEmitCount.eval(null, true);\r\n        this.perEmitInterval.value = this.emitterData.perEmitInterval.eval(null, true);\r\n        this.perEmitOffsetX.value = this.emitterData.perEmitOffsetX.eval(null, true);\r\n        this.angle.value = this.emitterData.angle.eval(null, true);\r\n        this.count.value = this.emitterData.count.eval(null, true);\r\n        this.arc.value = this.emitterData.arc.eval(null, true);\r\n        this.radius.value = this.emitterData.radius.eval(null, true);\r\n\r\n        this.bulletProp.resetFromData(this.bulletData);\r\n\r\n        this.notifyAll(forceNotify);\r\n    }\r\n\r\n    protected evalProperty(prop: Property<number>, value: ExpressionValue) {\r\n        // 为什么这样写，而不是直接：prop.setValue(value.eval(), ePropMask.ReEval);\r\n        // 是为了避免非必要的eval()调用\r\n        if (prop.canWrite(ePropMask.ReEval) && !value.isFixedValue) {\r\n            prop.value = value.eval();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * public apis\r\n     */\r\n    changeStatus(status: eEmitterStatus) {\r\n        if (this._status === status) return;\r\n\r\n        const oldStatus = this._status;\r\n        this._status = status;\r\n        this._statusElapsedTime = 0;\r\n        this._nextEmitTime = 0;\r\n        // Clear per-emit queue when changing status\r\n        this._perEmitBulletQueue = [];\r\n\r\n        if (status === eEmitterStatus.Prewarm) {\r\n            // 期望循环结束后，重置属性\r\n            this.resetProperties();\r\n        }\r\n        \r\n        if (status !== eEmitterStatus.None) {\r\n            // 所有其他状态，都尝试开始执行eventGroup\r\n            if (this.eventGroups.length > 0) {\r\n                this.eventGroups.forEach(group => group.tryStart());\r\n            }\r\n        }\r\n\r\n        if (this.onEmitterStatusChangedCallback != null) {\r\n            this.onEmitterStatusChangedCallback(this, oldStatus, status);\r\n        }\r\n    }\r\n\r\n    protected scheduleNextEmit() {\r\n        // Schedule the next emit after emitInterval\r\n        this._nextEmitTime = this._statusElapsedTime + this.emitInterval.value;\r\n        // console.log('scheduleNextEmit: ', this._nextEmitTime, ', ', this._statusElapsedTime, ', ', this.emitInterval.value);\r\n        \r\n        // re-eval\r\n        this.evalProperty(this.emitInterval, this.emitterData.emitInterval);\r\n    }\r\n\r\n    protected startEmitting() {\r\n        this._isEmitting = true;\r\n        // 下一次update时触发发射\r\n        // 或者在这里调用 this.tryEmit() && this.scheduleNextEmit(); 立即触发发射\r\n        // this.tryEmit();\r\n        // if (this.perEmitInterval.value <= 0) {\r\n        //     this.scheduleNextEmit();\r\n        // }\r\n        // else {\r\n        //     // 开始这一波\r\n        //     this._nextEmitTime = this._statusElapsedTime + 10000000;\r\n        // }\r\n        // // reset status time \r\n        // this._statusElapsedTime = 0;\r\n    }\r\n\r\n    protected stopEmitting() {\r\n        this._isEmitting = false;\r\n        // Clear the per-emit bullet queue\r\n        this._perEmitBulletQueue = [];\r\n        this.unscheduleAllCallbacks();\r\n    }\r\n\r\n    protected canEmit(): boolean {\r\n        // 检查是否可以触发发射\r\n        // Override this method in subclasses to add custom trigger conditions\r\n        return true;\r\n    }\r\n\r\n    protected emit(): void {\r\n        // re-eval\r\n        this.evalProperty(this.count, this.emitterData.count);\r\n        this.evalProperty(this.arc, this.emitterData.arc);\r\n        this.evalProperty(this.radius, this.emitterData.radius);\r\n        this.evalProperty(this.perEmitCount, this.emitterData.perEmitCount);\r\n        \r\n        if (this.perEmitInterval.value > 0) {\r\n            // Generate bullets in time-sorted order directly\r\n            for (let j = 0; j < this.perEmitCount.value; j++) {\r\n                const targetTime = this._statusElapsedTime + (this.perEmitInterval.value * j);\r\n                for (let i = 0; i < this.count.value; i++) {\r\n                    this._perEmitBulletQueue.push({\r\n                        index: i,\r\n                        perEmitIndex: j,\r\n                        targetTime: targetTime\r\n                    });\r\n                }\r\n                this.evalProperty(this.perEmitInterval, this.emitterData.perEmitInterval);\r\n            }\r\n        }\r\n        else {\r\n            // Immediate emission - no timing needed\r\n            for (let i = 0; i < this.count.value; i++) {\r\n                for (let j = 0; j < this.perEmitCount.value; j++) {\r\n                    this.emitSingle(i, j);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    protected processPerEmitQueue(): void {\r\n        // Process bullets that should be emitted based on current time\r\n        while (this._perEmitBulletQueue.length > 0) {\r\n            const nextBullet = this._perEmitBulletQueue[0];\r\n\r\n            // Check if it's time to emit this bullet\r\n            if (this._statusElapsedTime >= nextBullet.targetTime) {\r\n                // Remove from queue and emit\r\n                this._perEmitBulletQueue.shift();\r\n                this.emitSingle(nextBullet.index, nextBullet.perEmitIndex);\r\n            } else {\r\n                // No more bullets ready to emit yet\r\n                break;\r\n            }\r\n        }\r\n    }\r\n\r\n    protected tryEmit(): boolean {\r\n        if (this.canEmit()) {\r\n            this.emit();\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    protected emitSingle(index: number, perEmitIndex: number) {\r\n        const direction = this.getSpawnDirection(index);\r\n        const position = this.getSpawnPosition(index, perEmitIndex);\r\n        this.createBullet(direction, position);\r\n    }\r\n\r\n    /**\r\n     * Calculate the direction for a bullet at the given index\r\n     * @param index The index of the bullet (0 to count-1)\r\n     * @returns Direction vector {x, y}\r\n     */\r\n    getSpawnDirection(index: number): { x: number, y: number } {\r\n        // 期望如果配了公式，每次发射方向都随机下\r\n        this.evalProperty(this.angle, this.emitterData.angle);\r\n        // 计算发射方向\r\n        let angleOffset = 0;\r\n        // 360度或更大时，均匀分布在整个圆周上\r\n        // 小于360度时，在扇形范围内均匀分布\r\n        if (this.arc.value >= 360 && this.count.value > 0) {\r\n            angleOffset = (this.arc.value / this.count.value) * index;\r\n        } else if (this.count.value > 1) {\r\n            angleOffset = (this.arc.value / (this.count.value - 1)) * index - this.arc.value / 2;\r\n        }\r\n        const radian = degreesToRadians(this.angle.value + angleOffset);\r\n\r\n        return {\r\n            x: Math.cos(radian),\r\n            y: Math.sin(radian)\r\n        };\r\n    }\r\n    \r\n    /**\r\n     * Get the spawn position for a bullet at the given index\r\n     * odd number to the right, even number to the left\r\n     * @param index The index of the bullet (0 to count-1)\r\n     * @returns Position offset from emitter center\r\n     */\r\n    getSpawnPosition(index: number, perEmitIndex: number): { x: number, y: number } {\r\n        // add perEmitOffsetX by perEmitIndex, with the rules:\r\n        // by the following order:0, 1; 2, 0, 1; 2, 0, 1, 3;\r\n        const getEmitOffsetX = (perEmitIndex: number, perEmitCount: number, perEmitOffsetX: number) => {\r\n            if (perEmitCount <= 1 || perEmitOffsetX === 0) return 0;\r\n            const interval = perEmitOffsetX / (perEmitCount - 1);\r\n            //const middle = 0;\r\n\r\n            if (perEmitCount % 2 === 1) {\r\n                // 奇数情况\r\n                if (perEmitIndex === 0) return 0;\r\n                if (perEmitIndex % 2 === 0) {\r\n                    // 偶数索引在左边\r\n                    const stepsFromMiddle = Math.floor(perEmitIndex / 2);\r\n                    return -stepsFromMiddle * interval;\r\n                }\r\n                else {\r\n                    // 奇数索引在右边\r\n                    const stepsFromMiddle = Math.ceil(perEmitIndex / 2);\r\n                    return stepsFromMiddle * interval;\r\n                }\r\n            } else {\r\n                // 偶数情况\r\n                if (perEmitIndex === 0) return -interval / 2;\r\n                if (perEmitIndex % 2 === 0) {\r\n                    // 偶数索引在左边\r\n                    const stepsFromMiddle = Math.floor(perEmitIndex / 2);\r\n                    return -interval / 2 - stepsFromMiddle * interval;\r\n                }\r\n                else {\r\n                    // 奇数索引在右边\r\n                    const stepsFromMiddle = Math.floor(perEmitIndex / 2);\r\n                    return interval / 2 + stepsFromMiddle * interval;\r\n                }\r\n            }\r\n        }\r\n\r\n        this.evalProperty(this.perEmitOffsetX, this.emitterData.perEmitOffsetX);\r\n        const perEmitOffsetX = getEmitOffsetX(perEmitIndex, this.perEmitCount.value, this.perEmitOffsetX.value);\r\n        if (this.radius.value <= 0) {\r\n            return { x: perEmitOffsetX, y: 0 };\r\n        }\r\n\r\n        const direction = this.getSpawnDirection(index);\r\n        // 计算垂直于发射方向的向量（逆时针90度旋转）\r\n        const perpendicular = { x: -direction.y, y: direction.x };\r\n        if (this.radius.value <= 0) {\r\n            return {\r\n                x: perpendicular.x * perEmitOffsetX,\r\n                y: perpendicular.y * perEmitOffsetX\r\n            };\r\n        }\r\n\r\n        return {\r\n            x: direction.x * this.radius.value + perpendicular.x * perEmitOffsetX,\r\n            y: direction.y * this.radius.value + perpendicular.y * perEmitOffsetX\r\n        };\r\n    }\r\n\r\n    createBullet(direction: { x: number, y: number }, position: { x: number, y: number }): void {\r\n        if (!this._bulletPrefab) {\r\n            this._bulletPrefab = this.bulletData.prefab;\r\n            if (!this._bulletPrefab) {\r\n                if (EDITOR) {\r\n                    this.createBulletInEditor(direction, position);\r\n                }\r\n                return;\r\n            }\r\n        }\r\n\r\n        const bullet = this.instantiateBullet();\r\n        if (!bullet) return;\r\n\r\n        BulletSystem.onCreateBullet(this, bullet);\r\n        // Set bullet position relative to emitter\r\n        const emitterPos = this.node.getWorldPosition();\r\n        bullet.node.setWorldPosition(\r\n            emitterPos.x + position.x,\r\n            emitterPos.y + position.y,\r\n            emitterPos.z\r\n        );\r\n        bullet.prop.speedAngle.value = radiansToDegrees(Math.atan2(direction.y, direction.x));\r\n        bullet.prop.speed.value *= this.emitPower.value;\r\n        // 为什么需要在这里resetEventGroups?\r\n        // 因为EventGroups的条件初始化依赖上面先初始化子弹的属性\r\n        bullet.onReady();\r\n\r\n        if (this.onBulletCreatedCallback != null) {\r\n            this.onBulletCreatedCallback(bullet);\r\n        }\r\n    }\r\n\r\n    protected async createBulletInEditor(direction: { x: number, y: number }, position: { x: number, y: number }) {\r\n        // use a default bullet prefab\r\n        const prefabPath = 'db://assets/resources/game/prefabs/Bullet_New.prefab';\r\n        // @ts-ignore\r\n        Editor.Message.request('asset-db', 'query-uuid', prefabPath)\r\n            .then((uuid) => {\r\n                if (!uuid) return;\r\n                \r\n                assetManager.loadAny({ uuid: uuid }, (err, prefab) => {\r\n                    if (err) {\r\n                        console.error(err);\r\n                        return;\r\n                    }\r\n                    this._bulletPrefab = prefab;\r\n                    const bullet = this.instantiateBullet();\r\n                    if (!bullet) return;\r\n\r\n                    BulletSystem.onCreateBullet(this, bullet);\r\n                    // Set bullet position relative to emitter\r\n                    const emitterPos = this.node.getWorldPosition();\r\n                    bullet.node.setWorldPosition(\r\n                        emitterPos.x + position.x,\r\n                        emitterPos.y + position.y,\r\n                        emitterPos.z\r\n                    );\r\n                    bullet.prop.speedAngle.value = radiansToDegrees(Math.atan2(direction.y, direction.x));\r\n                    bullet.prop.speed.value *= this.emitPower.value;\r\n                    bullet.onReady();\r\n                });\r\n            });\r\n    }\r\n\r\n    protected instantiateBullet(): Bullet | null {\r\n        const bulletNode = ObjectPool.getNode(BulletSystem.bulletParent, this._bulletPrefab!);\r\n        if (!bulletNode) {\r\n            console.error(\"Emitter: Failed to instantiate bullet prefab\");\r\n            return null;\r\n        }\r\n\r\n        // Get the bullet component\r\n        const bullet = bulletNode.getComponent(Bullet);\r\n        if (!bullet) {\r\n            console.error(\"Emitter: Bullet prefab does not have Bullet component\");\r\n            bulletNode.destroy();\r\n            return null;\r\n        }\r\n\r\n        if (EDITOR) {\r\n            bulletNode.name = Emitter.kBulletNameInEditor;\r\n        }\r\n\r\n        return bullet;\r\n    }\r\n\r\n    protected loadConfigByID(emitterId: number) {\r\n        if (emitterId > 0 && MyApp.GetInstance() && MyApp.lubanMgr) {\r\n            this._emitterConfig = MyApp.lubanTables.TbResEmitter.get(emitterId);\r\n            if (!this._emitterConfig) {\r\n                logWarn(\"Emitter\", `loadConfigByID: emitter config not found, id=${emitterId}`);\r\n            }\r\n            // if (this._bulletConfig) {\r\n            //     MyApp.resMgr.load(this._bulletConfig.prefab, Prefab, (error: any, prefab: Prefab) => {\r\n            //         if (error) {\r\n            //             console.error(\"Emitter load bullet prefab err\", error);\r\n            //             return;\r\n            //         }\r\n            //         this._bulletPrefab = prefab;\r\n            //     });\r\n            // }\r\n        }\r\n        // else if (EDITOR) {\r\n        //     let lubanMgr = new LubanMgr();\r\n        //     lubanMgr.initInEditor().then(() => {\r\n        //         this._bulletConfig = lubanMgr.table.TbResBullet.get(bulletID);\r\n        //         if (this._bulletConfig) {\r\n        //             const prefabPath = 'db://assets/resources/' + this._bulletConfig.prefab + '.prefab';\r\n        //             // @ts-ignore\r\n        //             Editor.Message.request('asset-db', 'query-uuid', prefabPath)\r\n        //                 .then((uuid: string) => {\r\n        //                     assetManager.loadAny({ uuid: uuid }, (err, prefab) => {\r\n        //                         if (err) {\r\n        //                             console.error(err);\r\n        //                             return;\r\n        //                         }\r\n        //                         this._bulletPrefab = prefab;\r\n        //                     });\r\n        //                 });\r\n        //         }\r\n        //      });\r\n        // }\r\n    }\r\n\r\n    playEffect(prefab: Prefab, position: Vec3, rotation: Quat, duration: number) {\r\n        if (!prefab) return;\r\n\r\n        const effectNode = ObjectPool.getNode(this.node, prefab);\r\n        if (!effectNode) return;\r\n\r\n        effectNode.setWorldPosition(position);\r\n        effectNode.setWorldRotation(rotation);\r\n        // Play the effect and destroy it after duration\r\n        // effectNode.getComponent(ParticleSystem)?.play();\r\n        this.scheduleOnce(() => {\r\n            ObjectPool.returnNode(effectNode);\r\n        }, duration);\r\n    }\r\n\r\n    /**\r\n     * Return true if this.node is in screen\r\n     */\r\n    protected isInScreen(): boolean {\r\n        if (!this.isOnlyInScreen.value) return true;\r\n        \r\n        if (this._entity) {\r\n            return this._entity.isInScreen();\r\n        }\r\n\r\n        return GameConst.isPointInBattleView(this.node.position);\r\n    }\r\n\r\n    public tick(deltaTime: number): void {\r\n        if (!this.isActive || !this.isActive.value) {\r\n            return;\r\n        }\r\n\r\n        if (!this.isInScreen()) {\r\n            return;\r\n        }\r\n\r\n        switch (this._status) {\r\n            case eEmitterStatus.None:\r\n                this.updateStatusNone();\r\n                break;\r\n            case eEmitterStatus.Prewarm:\r\n                this.updateStatusPrewarm();\r\n                break;\r\n            case eEmitterStatus.Emitting:\r\n                this.updateStatusEmitting();\r\n                break;\r\n            case eEmitterStatus.LoopEndReached:\r\n                this.updateStatusLoopEndReached();\r\n                break;\r\n            case eEmitterStatus.Completed:\r\n                this.updateStatusCompleted();\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n\r\n        this.elapsedTime.value += deltaTime;\r\n        this._statusElapsedTime += deltaTime;\r\n        this._totalElapsedTime += deltaTime;\r\n\r\n        this.notifyAll();\r\n    }\r\n\r\n    protected updateStatusNone() {\r\n        if (this._statusElapsedTime >= this.initialDelay.value) {\r\n            this.changeStatus(eEmitterStatus.Prewarm);\r\n        }\r\n    }\r\n\r\n    protected updateStatusPrewarm() {\r\n        if (!this.isPreWarm.value)\r\n            this.changeStatus(eEmitterStatus.Emitting);\r\n        else {\r\n            if (this._statusElapsedTime >= this.preWarmDuration.value) {\r\n                this.changeStatus(eEmitterStatus.Emitting);\r\n            }\r\n        }\r\n    }\r\n\r\n    protected updateStatusEmitting() {\r\n        if (this._statusElapsedTime > this.emitDuration.value) {\r\n            this.stopEmitting();\r\n            if (this.isLoop.value)\r\n                this.changeStatus(eEmitterStatus.LoopEndReached);\r\n            else\r\n                this.changeStatus(eEmitterStatus.Completed);\r\n            return;\r\n        }\r\n\r\n        // Start emitting if not already started\r\n        if (!this._isEmitting) {\r\n            this.startEmitting();\r\n        }\r\n        else if (this._statusElapsedTime >= this._nextEmitTime) {\r\n            this.tryEmit();\r\n            if (this.perEmitInterval.value <= 0) {\r\n                this.scheduleNextEmit();\r\n            }\r\n            else {\r\n                // 开始这一波\r\n                this._nextEmitTime = this._statusElapsedTime + 10000000;\r\n            }\r\n        }\r\n\r\n        let wasEmitting = this._perEmitBulletQueue.length > 0;\r\n        // Process per-emit bullet queue based on precise timing\r\n        this.processPerEmitQueue();\r\n        if (wasEmitting && this._perEmitBulletQueue.length <= 0) {\r\n            this.scheduleNextEmit();\r\n        }\r\n    }\r\n\r\n    protected updateStatusLoopEndReached() {\r\n        if (this._statusElapsedTime >= this.loopInterval.value) {\r\n            this.changeStatus(eEmitterStatus.Prewarm);\r\n        }\r\n    }\r\n\r\n    protected updateStatusCompleted() {\r\n        // Do nothing or cleanup if needed\r\n        this.isActive.value = false;\r\n        this.isActive.notify();\r\n    }\r\n}\r\n"]}