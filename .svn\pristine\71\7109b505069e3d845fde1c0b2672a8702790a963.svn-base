
import { view, Vec3, Rect } from "cc";

class _GameConst {

    readonly ColliderDraw: boolean = false;
    readonly ActionFrameTime: number = 0.0333;

    designWidth: number = 750 // 设计分辨率
    designHeight: number = 1334 // 设计分辨率
    offsetWidth: number = 200 // 宽屏的时候，宽度最高多显示200像素

    readonly VIEWPORT_TOP: number = (1334 + 120) / 2; // 视口顶部位置, 往上增加120偏移, 这个用来激活event
    readonly VIEWPORT_LOAD_POS: number = 1334 + 1334 / 2; // 视口加载位置, 往上增加1334/2偏移

    readonly BATTLE_VIEW_TOP: number = this.VIEWPORT_TOP;
    readonly BATTLE_VIEW_BOTTOM: number = -this.VIEWPORT_TOP;
    readonly BATTLE_VIEW_LEFT: number = this.ViewBattleWidth / -2;
    readonly BATTLE_VIEW_RIGHT: number = this.ViewBattleWidth / 2;

    get ViewHeight() {
        return view.getVisibleSize().height
    }

    get ViewWidth() {
        return view.getVisibleSize().width;
    }

    get ViewTop() {
        return view.getVisibleSize().height / 2;
    }

    get ViewBottom() {
        return view.getVisibleSize().height / -2;
    }

    get ViewBattleWidth() {
        let width = view.getVisibleSize().width;
        return width + this.offsetWidth;
    }

    public isPointInBattleView(position: Vec3): boolean {
        return position.x > this.BATTLE_VIEW_LEFT && position.x < this.BATTLE_VIEW_RIGHT &&
            position.y > this.BATTLE_VIEW_BOTTOM && position.y < this.BATTLE_VIEW_TOP;
    }

    public isRectInBattleView(rect: Rect): boolean {
        return rect.x > this.BATTLE_VIEW_LEFT && rect.x + rect.width < this.BATTLE_VIEW_RIGHT &&
            rect.y > this.BATTLE_VIEW_BOTTOM && rect.y + rect.height < this.BATTLE_VIEW_TOP;
    }

}

export const GameConst = new _GameConst();