System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Graphics, Color, Component, instantiate, assetManager, CCObject, Wave, LubanMgr, EnemyPlane, eMoveEvent, PathData, PathEditor, _dec, _dec2, _dec3, _dec4, _class, _crd, ccclass, property, executeInEditMode, menu, requireComponent, WavePreview;

  function _reportPossibleCrUseOfWave(extras) {
    _reporterNs.report("Wave", "db://assets/bundles/common/script/game/wave/Wave", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLubanMgr(extras) {
    _reporterNs.report("LubanMgr", "db://assets/bundles/common/script/luban/LubanMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyPlane(extras) {
    _reporterNs.report("EnemyPlane", "db://assets/bundles/common/script/game/ui/plane/enemy/EnemyPlane", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeMoveEvent(extras) {
    _reporterNs.report("eMoveEvent", "db://assets/bundles/common/script/game/move/IMovable", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathData(extras) {
    _reporterNs.report("PathData", "db://assets/bundles/common/script/game/data/PathData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathEditor(extras) {
    _reporterNs.report("PathEditor", "db://assets/editor/level/wave/PathEditor", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Graphics = _cc.Graphics;
      Color = _cc.Color;
      Component = _cc.Component;
      instantiate = _cc.instantiate;
      assetManager = _cc.assetManager;
      CCObject = _cc.CCObject;
    }, function (_unresolved_2) {
      Wave = _unresolved_2.Wave;
    }, function (_unresolved_3) {
      LubanMgr = _unresolved_3.LubanMgr;
    }, function (_unresolved_4) {
      EnemyPlane = _unresolved_4.default;
    }, function (_unresolved_5) {
      eMoveEvent = _unresolved_5.eMoveEvent;
    }, function (_unresolved_6) {
      PathData = _unresolved_6.PathData;
    }, function (_unresolved_7) {
      PathEditor = _unresolved_7.PathEditor;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "e6727UuHORMv49xH6m7Wz2U", "WavePreview", undefined);

      __checkObsolete__(['_decorator', 'Node', 'Prefab', 'Graphics', 'Color', 'Component', 'Vec2', 'instantiate', 'assetManager', 'CCObject']);

      ({
        ccclass,
        property,
        executeInEditMode,
        menu,
        requireComponent
      } = _decorator);

      /// 用来创建和管理波次的所有飞机对象
      _export("WavePreview", WavePreview = (_dec = ccclass('WavePreview'), _dec2 = menu("怪物/编辑器/波次预览"), _dec3 = requireComponent(Graphics), _dec4 = executeInEditMode(), _dec(_class = _dec2(_class = _dec3(_class = _dec4(_class = class WavePreview extends Component {
        constructor(...args) {
          super(...args);
          this._luban = null;
          this._wave = null;
          this._graphics = null;
          // 这里的wave时编辑器play时，用来动态创建小怪的wave。
          this.isPreviewing = false;
          this.planePool = [];
          this.activePlane = [];
        }

        get luban() {
          if (this._luban == null) {
            this._luban = new (_crd && LubanMgr === void 0 ? (_reportPossibleCrUseOfLubanMgr({
              error: Error()
            }), LubanMgr) : LubanMgr)();
          }

          return this._luban;
        }

        onLoad() {
          this._wave = this.node.parent.getComponent(_crd && Wave === void 0 ? (_reportPossibleCrUseOfWave({
            error: Error()
          }), Wave) : Wave);
          this._graphics = this.getComponent(Graphics) || this.addComponent(Graphics);
        }

        reset() {
          this.node.removeAllChildren();
        }

        update(dt) {
          this.tickPreviewDraw();
          this.tickPreview(dt);
        }

        tickPreviewDraw() {
          var _this$_wave;

          const pathAsset = (_this$_wave = this._wave) == null ? void 0 : _this$_wave.waveData.pathAsset;

          if (pathAsset) {
            // draw path
            const path = (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
              error: Error()
            }), PathData) : PathData).fromJSON(pathAsset.json);
            const subdivided = path.getSubdividedPoints();
            const isWaveActive = this.isPreviewing && !this._wave.isSpawnCompleted && this._wave.waveElapsedTime > 0;
            const color = isWaveActive ? Color.GREEN : Color.RED;

            if (subdivided.length > 1) {
              (_crd && PathEditor === void 0 ? (_reportPossibleCrUseOfPathEditor({
                error: Error()
              }), PathEditor) : PathEditor).drawUniformPath(this._graphics, subdivided, color, path.closed, 10);
              const endPoint = subdivided[subdivided.length - 1];
              let prevPoint = subdivided[subdivided.length - 2];

              if (subdivided.length >= 5) {
                prevPoint = subdivided[subdivided.length - 5];
              }

              const direction = Math.atan2(endPoint.y - prevPoint.y, endPoint.x - prevPoint.x);
              (_crd && PathEditor === void 0 ? (_reportPossibleCrUseOfPathEditor({
                error: Error()
              }), PathEditor) : PathEditor).drawPathDirectionArrow(this._graphics, endPoint.position, direction, path.closed);
            } // draw path points


            this.drawPathPoints(path);
          }
        }

        drawPathPoints(path) {
          if (!path.points || path.points.length === 0) return; // 使用同一个 Graphics 绘制所有路径点

          for (let i = 0; i < path.points.length; i++) {
            const point = path.points[i];
            (_crd && PathEditor === void 0 ? (_reportPossibleCrUseOfPathEditor({
              error: Error()
            }), PathEditor) : PathEditor).drawPathPointAtPosition(this._graphics, point, point.x, point.y, false, // 不选中状态
            15, // 点大小稍小一些
            i, path.points.length, path.startIdx, path.endIdx);
          }
        }

        triggerPreview(posX, posY) {
          // console.log('WavePreview - triggerPreview: ', posX, posY, this._wave);
          if (!this._wave) {
            return;
          }

          this._wave.setCreatePlaneDelegate(async (planeId, pos, angle) => {
            this.createDummyPlane(this._wave, planeId, pos, angle);
          });

          this._wave.trigger(posX, posY);

          this.isPreviewing = true;
        }

        tickPreview(dt) {
          if (!this.isPreviewing) {
            return;
          }

          const dtInMiliseconds = dt * 1000;

          if (this._wave) {
            this._wave.tick(dtInMiliseconds);
          }

          this.activePlane.forEach(plane => {
            plane.moveCom.tick(dt);
          });
        }

        clearPreview() {
          // console.log('WavePreview - clearPreview: ', this.activePlane.length);
          this.isPreviewing = false; // destroy preivewWave

          this.activePlane.forEach(plane => {
            plane.node.destroy();
          });
          this.planePool.forEach(plane => {
            plane.node.destroy();
          }); // just in case

          this.node.removeAllChildren();
          this.activePlane = [];
          this.planePool = [];
        }

        createDummyPlane(wave, planeId, pos, angle) {
          // 对应"assets/editor/level/prefab/dummy_plane";
          // console.log('WavePreview - createDummyPlane: ', planeId, pos, angle);
          let plane = null;

          if (this.planePool.length > 0) {
            // 从对象池里拿一个dummy plane
            plane = this.planePool.pop();
            plane.node.active = true;
          } else {
            const dummy_plane_uuid = "698c56c6-6603-4e69-abaf-421b721ef307";
            assetManager.loadAny({
              uuid: dummy_plane_uuid
            }, async (err, prefab) => {
              if (err) {
                console.error("WavePreview createDummyPlane load prefab err", err);
                return;
              }

              try {
                // if (this.luban?.table == null) {
                //     await this.luban?.initInEditor();
                // }
                const canvas = this.node.scene.getChildByName("Canvas");

                if (!canvas) {
                  console.error("WavePreview createDummyPlane no canvas");
                  return;
                }

                const planeNode = instantiate(prefab);
                planeNode.hideFlags = CCObject.Flags.AllHideMasks;
                const plane = planeNode.getComponent(_crd && EnemyPlane === void 0 ? (_reportPossibleCrUseOfEnemyPlane({
                  error: Error()
                }), EnemyPlane) : EnemyPlane);

                if (plane) {
                  canvas.addChild(planeNode); // this.node.parent!.addChild(planeNode);
                  // const enemyData = new EnemyData(planeId, this.luban?.table.TbResEnemy.get(planeId));
                  // const prefab = await LevelEditorUtils.loadByPath<Prefab>(enemyData.recoursePrefab);
                  // plane.initPlane(enemyData, prefab!);

                  plane.initMove(pos.x, pos.y, angle);
                  plane.moveCom.removeAllListeners();
                  plane.moveCom.on((_crd && eMoveEvent === void 0 ? (_reportPossibleCrUseOfeMoveEvent({
                    error: Error()
                  }), eMoveEvent) : eMoveEvent).onBecomeInvisible, () => {
                    plane.node.active = false;
                    this.planePool.push(plane);
                  });

                  if (wave.path) {
                    plane.initPath(pos.x, pos.y, wave.path);
                  }

                  this.activePlane.push(plane);
                } else {
                  planeNode.destroy();
                }
              } catch (error) {
                console.error("WavePreview createDummyPlane err", error);
              }
            });
          }
        }

      }) || _class) || _class) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=41cd73be68eabcc1cac24b8cc6301d64aca50e3d.js.map