{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/wave/Wave.ts"], "names": ["_decorator", "Sprite", "Component", "Node", "CCObject", "Vec2", "assetManager", "SpriteFrame", "WaveData", "FormationGroup", "eSpawnOrder", "eWaveCompletion", "PathData", "GameIns", "WaveEventGroup", "WaveEventGroupContext", "PlaneEventType", "ccclass", "property", "executeInEditMode", "menu", "Wave", "displayName", "editor<PERSON><PERSON><PERSON>", "type", "_isSpawnCompleted", "_isAllEnemyDead", "_waveElapsedTime", "_nextSpawnTime", "_totalWeight", "_waveCompleteParam", "_spawnIndex", "_spawnQueue", "_randomRepeatPlaneID", "_originX", "_originY", "_formationGroup", "_path", "_eventGroups", "_eventGroupContext", "_enemyCreated", "_createPlaneDelegate", "isSpawnCompleted", "isAllEnemyDead", "waveElapsedTime", "formationGroup", "path", "onLoad", "waveData", "spawnOrder", "Random", "RandomRepeat", "spawnGroups", "for<PERSON>ach", "group", "weight", "selfWeight", "eventGroupData", "groupData", "push", "formationAsset", "Object", "assign", "json", "pathAsset", "fromJSON", "reset", "length", "random", "battleManager", "Math", "trigger", "x", "y", "waveCompletionParam", "eval", "randomWeight", "planeID", "waveCompletion", "SpawnCount", "i", "Sequential", "wave", "tryStart", "tick", "dtInMiliseconds", "tickSpawn", "spawnFromQueue", "spawnFromGroup", "spawnSingleFromQueue", "spawnInterval", "index", "spawnOffset", "spawnPosOffset", "spawnAngle", "createPlane", "planeId", "offset", "angle", "origin", "point", "points", "enemy", "enemyManager", "addPlane", "initPath", "initMove", "PlaneEventRegister", "Die", "onEnemyDie", "bind", "plane", "PlaneEventUnRegister", "indexOf", "splice", "setCreatePlaneDelegate", "func", "setupInEditor", "node", "getChildByName", "parent", "addComponent", "hideFlags", "Flags", "AllHideMasks", "setPosition", "spriteNode", "startPoint", "getStartPoint", "sprite", "getComponent", "waveIcon", "spriteFrame", "loadAny", "err", "asset", "console", "warn", "createWithImage"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAgCC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,Y,OAAAA,Y;AAA0BC,MAAAA,W,OAAAA,W;;AACnGC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,c,iBAAAA,c;AAAgCC,MAAAA,W,iBAAAA,W;AAAaC,MAAAA,e,iBAAAA,e;;AACvDC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,c,iBAAAA,c;AAAgBC,MAAAA,qB,iBAAAA,qB;;AAGhBC,MAAAA,c,iBAAAA,c;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA,iBAArB;AAAwCC,QAAAA;AAAxC,O,GAAiDpB,U;;sBAK1CqB,I,WAHZJ,OAAO,CAAC,MAAD,C,UACPG,IAAI,CAAC,OAAD,C,UACJD,iBAAiB,E,UAEbD,QAAQ,CAAC;AAACI,QAAAA,WAAW,EAAE,IAAd;AAAoBC,QAAAA,UAAU,EAAE;AAAhC,OAAD,C,UAERL,QAAQ,CAAC;AAACM,QAAAA,IAAI,EAAEjB,WAAP;AAAoBe,QAAAA,WAAW,EAAE,YAAjC;AAA+CC,QAAAA,UAAU,EAAE;AAA3D,OAAD,C,UAGRL,QAAQ,CAAC;AAACM,QAAAA,IAAI;AAAA;AAAA;AAAL,OAAD,C,yDATb,MAGaH,IAHb,SAG0BnB,SAH1B,CAGoC;AAAA;AAAA;;AAAA;;AAEM;AAFN;;AAAA;;AAShC;AACJ;AACA;AAXoC,eAYxBuB,iBAZwB,GAYK,KAZL;AAAA,eAcxBC,eAdwB,GAcG,KAdH;AAAA,eAiBxBC,gBAjBwB,GAiBG,CAjBH;AAAA,eAmBxBC,cAnBwB,GAmBC,CAnBD;AAAA,eAoBxBC,YApBwB,GAoBD,CApBC;AAqBhC;AArBgC,eAsBxBC,kBAtBwB,GAsBK,CAtBL;AAAA,eAuBxBC,WAvBwB,GAuBF,CAvBE;AAyBhC;AAzBgC,eA0BxBC,WA1BwB,GA0BA,EA1BA;AAAA,eA2BxBC,oBA3BwB,GA2BO,CA3BP;AA4BhC;AA5BgC,eA6BxBC,QA7BwB,GA6BL,CA7BK;AAAA,eA8BxBC,QA9BwB,GA8BL,CA9BK;AAAA,eA+BxBC,eA/BwB,GA+Be,IA/Bf;AAAA,eAiCxBC,KAjCwB,GAiCD,IAjCC;AAmChC;AAnCgC,eAoCxBC,YApCwB,GAoCS,EApCT;AAAA,eAqCxBC,kBArCwB,GAqCyB,IArCzB;AAsChC;AAtCgC,eAuCxBC,aAvCwB,GAuCM,EAvCN;AA+RhC;AA/RgC,eAgSxBC,oBAhSwB,GAgS2D,IAhS3D;AAAA;;AAaL,YAAhBC,gBAAgB,GAAG;AAAE,iBAAO,KAAKjB,iBAAZ;AAAgC;;AAEvC,YAAdkB,cAAc,GAAG;AAAE,iBAAO,KAAKjB,eAAZ;AAA8B;;AAGlC,YAAfkB,eAAe,GAAG;AAAE,iBAAO,KAAKjB,gBAAZ;AAA+B;;AAcrC,YAAdkB,cAAc,GAAwB;AAAE,iBAAO,KAAKT,eAAZ;AAA8B;;AAElE,YAAJU,IAAI,GAAkB;AAAE,iBAAO,KAAKT,KAAZ;AAAoB;;AAOvDU,QAAAA,MAAM,GAAG;AACL,cAAI,KAAKC,QAAT,EAAmB;AACf,gBAAI,KAAKA,QAAL,CAAcC,UAAd,KAA6B;AAAA;AAAA,4CAAYC,MAAzC,IAAmD,KAAKF,QAAL,CAAcC,UAAd,KAA6B;AAAA;AAAA,4CAAYE,YAAhG,EAA8G;AAC1G,mBAAKtB,YAAL,GAAoB,CAApB,CAD0G,CAE1G;;AACA,mBAAKmB,QAAL,CAAcI,WAAd,CAA0BC,OAA1B,CAAmCC,KAAD,IAAW;AACzC,qBAAKzB,YAAL,IAAqByB,KAAK,CAACC,MAA3B;AACAD,gBAAAA,KAAK,CAACE,UAAN,GAAmB,KAAK3B,YAAxB;AACH,eAHD;AAIH;;AAED,gBAAI,KAAKmB,QAAL,CAAcS,cAAlB,EAAkC;AAC9B,kBAAI,CAAC,KAAKlB,kBAAV,EAA8B;AAC1B,qBAAKA,kBAAL,GAA0B;AAAA;AAAA,qEAA1B;AACH;;AACD,mBAAKS,QAAL,CAAcS,cAAd,CAA6BJ,OAA7B,CAAsCK,SAAD,IAAe;AAChD,sBAAMJ,KAAK,GAAG;AAAA;AAAA,sDAAmB,KAAKf,kBAAxB,EAA6CmB,SAA7C,CAAd;;AACA,qBAAKpB,YAAL,CAAkBqB,IAAlB,CAAuBL,KAAvB;AACH,eAHD;AAIH;;AAED,gBAAI,KAAKN,QAAL,CAAcY,cAAlB,EAAkC;AAC9B,mBAAKxB,eAAL,GAAuB;AAAA;AAAA,qDAAvB;AACAyB,cAAAA,MAAM,CAACC,MAAP,CAAc,KAAK1B,eAAnB,EAAoC,KAAKY,QAAL,CAAcY,cAAd,CAA6BG,IAAjE;AACH;;AAED,gBAAI,KAAKf,QAAL,CAAcgB,SAAlB,EAA6B;AACzB,mBAAK3B,KAAL,GAAa;AAAA;AAAA,wCAAS4B,QAAT,CAAkB,KAAKjB,QAAL,CAAcgB,SAAd,CAAwBD,IAA1C,CAAb;AACH;AACJ;AACJ;;AAEOG,QAAAA,KAAK,GAAG;AACZ,eAAKzC,iBAAL,GAAyB,KAAzB;AACA,eAAKC,eAAL,GAAuB,KAAvB;AACA,eAAKC,gBAAL,GAAwB,CAAxB;AACA,eAAKC,cAAL,GAAsB,CAAtB;AACA,eAAKG,WAAL,GAAmB,CAAnB;AACA,eAAKE,oBAAL,GAA4B,CAA5B;AACA,eAAKD,WAAL,CAAiBmC,MAAjB,GAA0B,CAA1B;AACA,eAAK5B,kBAAL,IAA4B,KAAKA,kBAAL,CAAyB2B,KAAzB,EAA5B;;AAEA,eAAK5B,YAAL,CAAkBe,OAAlB,CAA2BC,KAAD,IAAW;AACjCA,YAAAA,KAAK,CAACY,KAAN;AACH,WAFD;;AAIA,cAAI,KAAK1B,aAAL,CAAmB2B,MAAnB,GAA4B,CAAhC,EAAmC;AAC/B,iBAAK3B,aAAL,CAAmB2B,MAAnB,GAA4B,CAA5B;AACH;AACJ;;AAEY,eAANC,MAAM,GAAW;AACpB,cAAI;AAAA;AAAA,kCAAQC,aAAZ,EAA2B;AACvB,mBAAO;AAAA;AAAA,oCAAQA,aAAR,CAAsBD,MAAtB,EAAP;AACH;;AAED,iBAAOE,IAAI,CAACF,MAAL,EAAP;AACH;;AAEDG,QAAAA,OAAO,CAACC,CAAD,EAAYC,CAAZ,EAAuB;AAC1B,eAAKP,KAAL;AACA,eAAKhC,QAAL,GAAgBsC,CAAhB;AACA,eAAKrC,QAAL,GAAgBsC,CAAhB,CAH0B,CAK1B;AAEA;;AACA,cAAI,KAAKzB,QAAT,EAAmB;AACf,iBAAKlB,kBAAL,GAA0B,KAAKkB,QAAL,CAAc0B,mBAAd,CAAkCC,IAAlC,EAA1B,CADe,CAGf;;AACA,gBAAI,KAAK3B,QAAL,CAAcC,UAAd,KAA6B;AAAA;AAAA,4CAAYE,YAA7C,EAA2D;AACvD,oBAAMyB,YAAY,GAAGvD,IAAI,CAAC+C,MAAL,KAAgB,KAAKvC,YAA1C;;AACA,mBAAK,MAAMyB,KAAX,IAAoB,KAAKN,QAAL,CAAcI,WAAlC,EAA+C;AAC3C,oBAAIwB,YAAY,IAAItB,KAAK,CAACE,UAA1B,EAAsC;AAClC,uBAAKvB,oBAAL,GAA4BqB,KAAK,CAACuB,OAAlC;AACA;AACH;AACJ;AACJ;;AAED,gBAAI,KAAK7B,QAAL,CAAc8B,cAAd,KAAiC;AAAA;AAAA,oDAAgBC,UAArD,EAAiE;AAC7D,kBAAI,KAAK/B,QAAL,CAAcC,UAAd,KAA6B;AAAA;AAAA,8CAAYC,MAA7C,EAAqD;AACjD,qBAAK,IAAI8B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKlD,kBAAzB,EAA6CkD,CAAC,EAA9C,EAAkD;AAC9C,wBAAMJ,YAAY,GAAGvD,IAAI,CAAC+C,MAAL,KAAgB,KAAKvC,YAA1C;;AACA,uBAAK,MAAMyB,KAAX,IAAoB,KAAKN,QAAL,CAAcI,WAAlC,EAA+C;AAC3C,wBAAIwB,YAAY,IAAItB,KAAK,CAACE,UAA1B,EAAsC;AAClC,2BAAKxB,WAAL,CAAiB2B,IAAjB,CAAsBL,KAAK,CAACuB,OAA5B;;AACA;AACH;AACJ;AACJ;AACJ,eAVD,MAWK,IAAI,KAAK7B,QAAL,CAAcC,UAAd,KAA6B;AAAA;AAAA,8CAAYgC,UAA7C,EAAyD;AAC1D,qBAAK,IAAID,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKlD,kBAAzB,EAA6CkD,CAAC,EAA9C,EAAkD;AAC9C;AACA,uBAAKhD,WAAL,CAAiB2B,IAAjB,CAAsB,KAAKX,QAAL,CAAcI,WAAd,CAA0B4B,CAAC,GAAG,KAAKhC,QAAL,CAAcI,WAAd,CAA0Be,MAAxD,EAAgEU,OAAtF;AACH;AACJ,eALI,MAMA;AACD,qBAAK,IAAIG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKlD,kBAAzB,EAA6CkD,CAAC,EAA9C,EAAkD;AAC9C,uBAAKhD,WAAL,CAAiB2B,IAAjB,CAAsB,KAAK1B,oBAA3B;AACH;AACJ;AACJ,aArCc,CAuCf;;;AACA,gBAAI,CAAC,KAAKG,eAAN,IAAyB,KAAKY,QAAL,CAAcY,cAA3C,EAA2D;AACvD,mBAAKxB,eAAL,GAAuB;AAAA;AAAA,qDAAvB;AACAyB,cAAAA,MAAM,CAACC,MAAP,CAAc,KAAK1B,eAAnB,EAAoC,KAAKY,QAAL,CAAcY,cAAd,CAA6BG,IAAjE;AACH;;AAED,gBAAI,CAAC,KAAK1B,KAAN,IAAe,KAAKW,QAAL,CAAcgB,SAAjC,EAA4C;AACxC,mBAAK3B,KAAL,GAAa;AAAA;AAAA,wCAAS4B,QAAT,CAAkB,KAAKjB,QAAL,CAAcgB,SAAd,CAAwBD,IAA1C,CAAb;AACH;AACJ;;AAED,eAAKxB,kBAAL,KAA4B,KAAKA,kBAAL,CAAyB2C,IAAzB,GAAgC,IAA5D;;AACA,eAAK5C,YAAL,CAAkBe,OAAlB,CAA2BC,KAAD,IAAW;AACjCA,YAAAA,KAAK,CAAC6B,QAAN;AACH,WAFD;AAGH,SAlK+B,CAoKhC;;;AACAC,QAAAA,IAAI,CAACC,eAAD,EAA0B;AAC1B,cAAI,CAAC,KAAK5D,iBAAV,EAA6B;AACzB,iBAAK6D,SAAL,CAAeD,eAAf;AACH;AACJ;;AAEOC,QAAAA,SAAS,CAACD,eAAD,EAA0B;AACvC,eAAK1D,gBAAL,IAAyB0D,eAAzB;;AACA,cAAI,KAAKrC,QAAL,CAAc8B,cAAd,KAAiC;AAAA;AAAA,kDAAgBC,UAArD,EAAiE;AAC7D;AACA,gBAAI,KAAKhD,WAAL,IAAoB,KAAKD,kBAA7B,EAAiD;AAC7C,mBAAKL,iBAAL,GAAyB,IAAzB;AACH,aAFD,MAEO;AACH,kBAAI,KAAKE,gBAAL,IAAyB,KAAKC,cAAlC,EAAkD;AAC9C,qBAAK2D,cAAL;AACH;AACJ;AACJ,WATD,MASO;AACH;AACA,gBAAI,KAAK5D,gBAAL,IAAyB,KAAKG,kBAAlC,EAAsD;AAClD,mBAAKL,iBAAL,GAAyB,IAAzB;AACH,aAFD,MAEO;AACH,kBAAI,KAAKE,gBAAL,IAAyB,KAAKC,cAAlC,EAAkD;AAC9C,qBAAK4D,cAAL;AACH;AACJ;AACJ;;AAED,cAAI,KAAKlD,YAAL,CAAkB6B,MAAlB,GAA2B,CAA/B,EAAkC;AAC9B,iBAAK,IAAIa,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK1C,YAAL,CAAkB6B,MAAtC,EAA8Ca,CAAC,EAA/C,EAAmD;AAC/C,mBAAK1C,YAAL,CAAkB0C,CAAlB,EAAqBI,IAArB,CAA0BC,eAA1B;AACH;AACJ;AACJ;;AAEOE,QAAAA,cAAc,GAAS;AAC3B,eAAKE,oBAAL,CAA0B,KAAK1D,WAAL,GAAmB,KAAKC,WAAL,CAAiBmC,MAA9D;AACA,eAAKvC,cAAL,GAAsB,KAAKD,gBAAL,GAAwB,KAAKqB,QAAL,CAAc0C,aAAd,CAA4Bf,IAA5B,EAA9C;AACH;;AAEOc,QAAAA,oBAAoB,CAACE,KAAD,EAAsB;AAC9C,cAAIA,KAAK,IAAI,KAAK3D,WAAL,CAAiBmC,MAA9B,EAAsC;AAClC;AACH;;AAED,cAAIyB,WAAW,GAAG,KAAK5C,QAAL,CAAc6C,cAAhC;AACA,cAAIC,UAAU,GAAG,KAAK9C,QAAL,CAAc8C,UAAd,CAAyBnB,IAAzB,EAAjB,CAN8C,CAO9C;;AAEA,eAAKoB,WAAL,CAAiB,KAAK/D,WAAL,CAAiB2D,KAAjB,CAAjB,EAA0CC,WAA1C,EAAuDE,UAAvD;AACH;;AAE2B,cAAdN,cAAc,GAAkB;AAC1C,eAAK5D,cAAL,GAAsB,KAAKD,gBAAL,GAAwB,KAAKqB,QAAL,CAAc0C,aAAd,CAA4Bf,IAA5B,EAA9C;AAEA,cAAIiB,WAAW,GAAG,KAAK5C,QAAL,CAAc6C,cAAhC;AACA,cAAIC,UAAU,GAAG,KAAK9C,QAAL,CAAc8C,UAAd,CAAyBnB,IAAzB,EAAjB;;AAEA,cAAI,KAAK3B,QAAL,CAAcC,UAAd,KAA6B;AAAA;AAAA,0CAAYC,MAA7C,EAAqD;AACjD,kBAAM0B,YAAY,GAAGvD,IAAI,CAAC+C,MAAL,KAAgB,KAAKvC,YAA1C;;AACA,iBAAK,MAAMyB,KAAX,IAAoB,KAAKN,QAAL,CAAcI,WAAlC,EAA+C;AAC3C,kBAAIwB,YAAY,IAAItB,KAAK,CAACE,UAA1B,EAAsC;AAClC,sBAAM,KAAKuC,WAAL,CAAiBzC,KAAK,CAACuB,OAAvB,EAAgCe,WAAhC,EAA6CE,UAA7C,CAAN;AACA;AACH;AACJ;AACJ,WARD,MAQO,IAAI,KAAK9C,QAAL,CAAcC,UAAd,KAA6B;AAAA;AAAA,0CAAYE,YAA7C,EAA2D;AAC9D,kBAAM,KAAK4C,WAAL,CAAiB,KAAK9D,oBAAtB,EAA4C2D,WAA5C,EAAyDE,UAAzD,CAAN;AACH,WAFM,MAEA;AACH,kBAAM,KAAKC,WAAL,CAAiB,KAAK/C,QAAL,CAAcI,WAAd,CAA0B,KAAKrB,WAAL,GAAmB,KAAKiB,QAAL,CAAcI,WAAd,CAA0Be,MAAvE,EAA+EU,OAAhG,EAAyGe,WAAzG,EAAsHE,UAAtH,CAAN;AACH;AACJ;;AAEwB,cAAXC,WAAW,CAACC,OAAD,EAAkBC,MAAlB,EAAgCC,KAAhC,EAA+C;AACpE,cAAIC,MAAY,GAAG,IAAI9F,IAAJ,CAAS,KAAK6B,QAAd,EAAwB,KAAKC,QAA7B,CAAnB,CADoE,CAEpE;AACA;AACA;AACA;AAEA;;AACA,cAAI,KAAKC,eAAT,EAA0B;AACtB,kBAAMgE,KAAK,GAAG,KAAKhE,eAAL,CAAqBiE,MAArB,CAA4B,KAAKtE,WAAL,GAAmB,KAAKK,eAAL,CAAqBiE,MAArB,CAA4BlC,MAA3E,CAAd;AACA8B,YAAAA,MAAM,CAACzB,CAAP,IAAY4B,KAAK,CAAC5B,CAAlB;AACAyB,YAAAA,MAAM,CAACxB,CAAP,IAAY2B,KAAK,CAAC3B,CAAlB;AACH;;AAED0B,UAAAA,MAAM,CAAC3B,CAAP,IAAYyB,MAAM,CAACzB,CAAnB;AACA2B,UAAAA,MAAM,CAAC1B,CAAP,IAAYwB,MAAM,CAACxB,CAAnB;AAEA,eAAK1C,WAAL;;AACA,cAAI,KAAKU,oBAAT,EAA+B;AAC3B,iBAAKA,oBAAL,CAA0BuD,OAA1B,EAAmC,IAAI3F,IAAJ,CAAS8F,MAAM,CAAC3B,CAAhB,EAAmByB,MAAM,CAACxB,CAA1B,CAAnC,EAAiEyB,KAAjE,EAD2B,CAC8C;;;AACzE;AACH,WArBmE,CAuBpE;;;AACA,cAAII,KAAK,GAAG,MAAM;AAAA;AAAA,kCAAQC,YAAR,CAAqBC,QAArB,CAA8BR,OAA9B,CAAlB;;AACA,cAAIM,KAAJ,EAAW;AACP,gBAAI,KAAKjE,KAAT,EAAgB;AACZ;AACAiE,cAAAA,KAAK,CAACG,QAAN,CAAeN,MAAM,CAAC3B,CAAtB,EAAyByB,MAAM,CAACxB,CAAhC,EAAmC,KAAKpC,KAAxC;AACH,aAHD,MAGO;AACHiE,cAAAA,KAAK,CAACI,QAAN,CAAeP,MAAM,CAAC3B,CAAtB,EAAyB2B,MAAM,CAAC1B,CAAhC,EAAmCyB,KAAnC;AACH;;AACDI,YAAAA,KAAK,CAACK,kBAAN,CAAyB;AAAA;AAAA,kDAAeC,GAAxC,EAA6C,KAAKC,UAAL,CAAgBC,IAAhB,CAAqB,IAArB,CAA7C;;AACA,iBAAKtE,aAAL,CAAmBmB,IAAnB,CAAwB2C,KAAxB,EARO,CAQyB;;AACnC;AACJ;;AAEOO,QAAAA,UAAU,CAACE,KAAD,EAAoB;AAClCA,UAAAA,KAAK,CAACC,oBAAN,CAA2B;AAAA;AAAA,gDAAeJ,GAA1C,EAA+C,KAAKC,UAAL,CAAgBC,IAAhB,CAAqB,IAArB,CAA/C;;AACA,gBAAMnB,KAAK,GAAG,KAAKnD,aAAL,CAAmByE,OAAnB,CAA2BF,KAA3B,CAAd;;AACA,cAAIpB,KAAK,KAAK,CAAC,CAAf,EAAkB;AACd,iBAAKnD,aAAL,CAAmB0E,MAAnB,CAA0BvB,KAA1B,EAAiC,CAAjC;;AAEA,gBAAI,KAAKnD,aAAL,CAAmB2B,MAAnB,KAA8B,CAA9B,IAAmC,KAAK1C,iBAA5C,EAA+D;AAC3D,mBAAKC,eAAL,GAAuB,IAAvB;AACH;AACJ;AACJ;;AAIMyF,QAAAA,sBAAsB,CAACC,IAAD,EAA4D;AACrF,eAAK3E,oBAAL,GAA4B2E,IAA5B;AACH;;AAEMC,QAAAA,aAAa,GAAG;AACnB;AACA,cAAIC,IAAI,GAAG,KAAKA,IAAL,CAAUC,cAAV,CAAyB,aAAzB,CAAX;;AACA,cAAI,CAACD,IAAL,EAAW;AACPA,YAAAA,IAAI,GAAG,IAAInH,IAAJ,CAAS,aAAT,CAAP;AACAmH,YAAAA,IAAI,CAACE,MAAL,GAAc,KAAKF,IAAnB;AACAA,YAAAA,IAAI,CAACG,YAAL,CAAkB,aAAlB;AACAH,YAAAA,IAAI,CAACI,SAAL,GAAiBtH,QAAQ,CAACuH,KAAT,CAAeC,YAAhC;AACAN,YAAAA,IAAI,CAACO,WAAL,CAAiB,CAAjB,EAAoB,CAApB,EAAuB,CAAvB;AACH,WATkB,CAUnB;AACA;;;AAEA,cAAIC,UAAU,GAAG,KAAKR,IAAL,CAAUC,cAAV,CAAyB,QAAzB,CAAjB;;AACA,cAAI,CAACO,UAAL,EAAiB;AACbA,YAAAA,UAAU,GAAG,IAAI3H,IAAJ,CAAS,QAAT,CAAb;AACA2H,YAAAA,UAAU,CAACN,MAAX,GAAoB,KAAKF,IAAzB;AACAQ,YAAAA,UAAU,CAACJ,SAAX,GAAuBtH,QAAQ,CAACuH,KAAT,CAAeC,YAAtC;;AAEA,gBAAI,KAAK5E,QAAL,CAAcgB,SAAd,IAA2B,CAAC,KAAK3B,KAArC,EAA4C;AACxC,mBAAKA,KAAL,GAAa;AAAA;AAAA,wCAAS4B,QAAT,CAAkB,KAAKjB,QAAL,CAAcgB,SAAd,CAAwBD,IAA1C,CAAb;AACH;;AACD,gBAAI,KAAK1B,KAAT,EAAgB;AACZ,oBAAM0F,UAAU,GAAG,KAAK1F,KAAL,CAAW2F,aAAX,EAAnB;;AACA,kBAAID,UAAJ,EAAgB;AACZD,gBAAAA,UAAU,CAACD,WAAX,CAAuBE,UAAU,CAACvD,CAAlC,EAAqCuD,UAAU,CAACtD,CAAhD,EAAmD,CAAnD;AACH;AACJ;AACJ;;AAED,gBAAMwD,MAAM,GAAGH,UAAU,CAACI,YAAX,CAAwBjI,MAAxB,KAAmC6H,UAAU,CAACL,YAAX,CAAwBxH,MAAxB,CAAlD;;AACA,cAAI,KAAKkI,QAAT,EAAmB;AACfF,YAAAA,MAAM,CAACG,WAAP,GAAqB,KAAKD,QAA1B;AACH,WAFD,MAEO;AACH;AACA7H,YAAAA,YAAY,CAAC+H,OAAb,CAAiC,sCAAjC,EACI,CAACC,GAAD,EAAiBC,KAAjB,KAAuC;AACnC,kBAAID,GAAJ,EAAS;AACLE,gBAAAA,OAAO,CAACC,IAAR,CAAaH,GAAb;AACA;AACH;;AAEDL,cAAAA,MAAM,CAACG,WAAP,GAAqB7H,WAAW,CAACmI,eAAZ,CAA4BH,KAA5B,CAArB;AACH,aARL;AASH;AACJ;;AAlV+B,O;;;;;iBAEb,E;;;;;;;iBAEU,I;;;;;;;iBAGC;AAAA;AAAA,qC", "sourcesContent": ["import { _decorator, CCBoolean, CCFloat, Sprite, Component, Node, CCObject, Vec2, assetManager, ImageAsset, SpriteFrame } from 'cc';\r\nimport { WaveData, FormationGroup, FormationPoint, eSpawnOrder, eWaveCompletion } from '../data/WaveData';\r\nimport { PathData } from '../data/PathData';\r\nimport { GameIns } from 'db://assets/bundles/common/script/game/GameIns';\r\nimport { WaveEventGroup, WaveEventGroupContext } from './WaveEventGroup';\r\nimport EnemyPlane from 'db://assets/bundles/common/script/game/ui/plane/enemy/EnemyPlane';\r\nimport PlaneBase from 'db://assets/bundles/common/script/game/ui/plane/PlaneBase';\r\nimport { PlaneEventType } from 'db://assets/bundles/common/script/game/ui/plane/event/PlaneEventType';\r\nconst { ccclass, property, executeInEditMode, menu } = _decorator;\r\n\r\n@ccclass('Wave')\r\n@menu(\"怪物/波次\")\r\n@executeInEditMode()\r\nexport class Wave extends Component {\r\n    @property({displayName: '名称', editorOnly: true})\r\n    waveName: string = '';                // 备注(策划用)\r\n    @property({type: SpriteFrame, displayName: '显示图片(仅编辑器)', editorOnly: true})\r\n    waveIcon: SpriteFrame|null = null;\r\n\r\n    @property({type:WaveData})\r\n    readonly waveData: WaveData = new WaveData();\r\n\r\n    /*\r\n     * 以下是新的wave逻辑, 旧的逻辑主要在WaveManager与EnemyWave\r\n     */\r\n    private _isSpawnCompleted: boolean = false;\r\n    public get isSpawnCompleted() { return this._isSpawnCompleted; }\r\n    private _isAllEnemyDead: boolean = false;\r\n    public get isAllEnemyDead() { return this._isAllEnemyDead; }\r\n    \r\n    private _waveElapsedTime: number = 0;\r\n    public get waveElapsedTime() { return this._waveElapsedTime; }\r\n    private _nextSpawnTime: number = 0;\r\n    private _totalWeight: number = 0;\r\n    // 这个参数可能是时间，也可能是数量，取决于waveData.waveCompletion\r\n    private _waveCompleteParam: number = 0;\r\n    private _spawnIndex: number = 0;\r\n\r\n    // 用在waveCompletion == SpawnCount时的队列\r\n    private _spawnQueue: number[] = [];\r\n    private _randomRepeatPlaneID: number = 0;\r\n    // 当前wave的起点位置\r\n    private _originX: number = 0;\r\n    private _originY: number = 0;\r\n    private _formationGroup: FormationGroup|null = null;\r\n    public get formationGroup(): FormationGroup|null { return this._formationGroup; }\r\n    private _path: PathData|null = null;\r\n    public get path(): PathData|null { return this._path; }\r\n    // 事件组\r\n    private _eventGroups: WaveEventGroup[] = [];\r\n    private _eventGroupContext: WaveEventGroupContext|null = null;\r\n    // 怪物\r\n    private _enemyCreated: EnemyPlane[] = [];\r\n\r\n    onLoad() {\r\n        if (this.waveData) {\r\n            if (this.waveData.spawnOrder === eSpawnOrder.Random || this.waveData.spawnOrder === eSpawnOrder.RandomRepeat) {\r\n                this._totalWeight = 0;\r\n                // add up _totalWeight if is random\r\n                this.waveData.spawnGroups.forEach((group) => {\r\n                    this._totalWeight += group.weight;\r\n                    group.selfWeight = this._totalWeight;\r\n                });\r\n            }\r\n            \r\n            if (this.waveData.eventGroupData) {\r\n                if (!this._eventGroupContext) {\r\n                    this._eventGroupContext = new WaveEventGroupContext();\r\n                }\r\n                this.waveData.eventGroupData.forEach((groupData) => {\r\n                    const group = new WaveEventGroup(this._eventGroupContext!, groupData);\r\n                    this._eventGroups.push(group);\r\n                });\r\n            }\r\n\r\n            if (this.waveData.formationAsset) {\r\n                this._formationGroup = new FormationGroup();\r\n                Object.assign(this._formationGroup, this.waveData.formationAsset.json);\r\n            }\r\n\r\n            if (this.waveData.pathAsset) {\r\n                this._path = PathData.fromJSON(this.waveData.pathAsset.json);\r\n            }\r\n        }\r\n    }\r\n\r\n    private reset() {\r\n        this._isSpawnCompleted = false;\r\n        this._isAllEnemyDead = false;\r\n        this._waveElapsedTime = 0;\r\n        this._nextSpawnTime = 0;\r\n        this._spawnIndex = 0;\r\n        this._randomRepeatPlaneID = 0;\r\n        this._spawnQueue.length = 0;\r\n        this._eventGroupContext && (this._eventGroupContext!.reset());\r\n        \r\n        this._eventGroups.forEach((group) => {\r\n            group.reset();\r\n        });\r\n\r\n        if (this._enemyCreated.length > 0) {\r\n            this._enemyCreated.length = 0;\r\n        }\r\n    }\r\n\r\n    static random(): number {\r\n        if (GameIns.battleManager) {\r\n            return GameIns.battleManager.random();\r\n        }\r\n\r\n        return Math.random();\r\n    }\r\n\r\n    trigger(x: number, y: number) {\r\n        this.reset();\r\n        this._originX = x;\r\n        this._originY = y;\r\n\r\n        // console.log('wave triggered at x: ' + x + ', y: ' + y);\r\n\r\n        // 对于固定数量的波次，可以预先生成队列，每次从里面取即可\r\n        if (this.waveData) {\r\n            this._waveCompleteParam = this.waveData.waveCompletionParam.eval();\r\n            \r\n            // 如果是RandomRepeat类型(随机一次,然后重复),我们先把这个飞机ID摇出来\r\n            if (this.waveData.spawnOrder === eSpawnOrder.RandomRepeat) {\r\n                const randomWeight = Wave.random() * this._totalWeight;\r\n                for (const group of this.waveData.spawnGroups) {\r\n                    if (randomWeight <= group.selfWeight) {\r\n                        this._randomRepeatPlaneID = group.planeID;\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n\r\n            if (this.waveData.waveCompletion === eWaveCompletion.SpawnCount) {\r\n                if (this.waveData.spawnOrder === eSpawnOrder.Random) {\r\n                    for (let i = 0; i < this._waveCompleteParam; i++) {\r\n                        const randomWeight = Wave.random() * this._totalWeight;\r\n                        for (const group of this.waveData.spawnGroups) {\r\n                            if (randomWeight <= group.selfWeight) {\r\n                                this._spawnQueue.push(group.planeID);\r\n                                break;\r\n                            }\r\n                        }\r\n                    }\r\n                } \r\n                else if (this.waveData.spawnOrder === eSpawnOrder.Sequential) {\r\n                    for (let i = 0; i < this._waveCompleteParam; i++) {\r\n                        // 通过取余实现循环\r\n                        this._spawnQueue.push(this.waveData.spawnGroups[i % this.waveData.spawnGroups.length].planeID);\r\n                    }\r\n                }\r\n                else {\r\n                    for (let i = 0; i < this._waveCompleteParam; i++) {\r\n                        this._spawnQueue.push(this._randomRepeatPlaneID);\r\n                    }\r\n                }\r\n            }\r\n\r\n            // 编辑器下预览这里可能为空\r\n            if (!this._formationGroup && this.waveData.formationAsset) {\r\n                this._formationGroup = new FormationGroup();\r\n                Object.assign(this._formationGroup, this.waveData.formationAsset.json);\r\n            }\r\n\r\n            if (!this._path && this.waveData.pathAsset) {\r\n                this._path = PathData.fromJSON(this.waveData.pathAsset.json);\r\n            }\r\n        }\r\n\r\n        this._eventGroupContext && (this._eventGroupContext!.wave = this);\r\n        this._eventGroups.forEach((group) => {\r\n            group.tryStart();\r\n        });\r\n    }\r\n\r\n    // tick wave\r\n    tick(dtInMiliseconds: number) {\r\n        if (!this._isSpawnCompleted) {\r\n            this.tickSpawn(dtInMiliseconds);\r\n        }\r\n    }\r\n    \r\n    private tickSpawn(dtInMiliseconds: number) {\r\n        this._waveElapsedTime += dtInMiliseconds;\r\n        if (this.waveData.waveCompletion === eWaveCompletion.SpawnCount) {\r\n            // 产出固定数量的波次\r\n            if (this._spawnIndex >= this._waveCompleteParam) {\r\n                this._isSpawnCompleted = true;\r\n            } else {\r\n                if (this._waveElapsedTime >= this._nextSpawnTime) {\r\n                    this.spawnFromQueue();\r\n                }\r\n            }\r\n        } else {\r\n            // 完全根据时间的波次\r\n            if (this._waveElapsedTime >= this._waveCompleteParam) {\r\n                this._isSpawnCompleted = true;\r\n            } else {\r\n                if (this._waveElapsedTime >= this._nextSpawnTime) {\r\n                    this.spawnFromGroup();\r\n                }\r\n            }\r\n        }\r\n\r\n        if (this._eventGroups.length > 0) {\r\n            for (let i = 0; i < this._eventGroups.length; i++) {\r\n                this._eventGroups[i].tick(dtInMiliseconds);\r\n            }\r\n        }\r\n    }\r\n\r\n    private spawnFromQueue(): void {        \r\n        this.spawnSingleFromQueue(this._spawnIndex % this._spawnQueue.length);\r\n        this._nextSpawnTime = this._waveElapsedTime + this.waveData.spawnInterval.eval();\r\n    }\r\n\r\n    private spawnSingleFromQueue(index: number): void {\r\n        if (index >= this._spawnQueue.length) {\r\n            return;\r\n        }\r\n\r\n        let spawnOffset = this.waveData.spawnPosOffset;\r\n        let spawnAngle = this.waveData.spawnAngle.eval();\r\n        // let spawnSpeed = this.waveData.spawnSpeed.eval();\r\n\r\n        this.createPlane(this._spawnQueue[index], spawnOffset, spawnAngle);\r\n    }\r\n\r\n    private async spawnFromGroup(): Promise<void> {\r\n        this._nextSpawnTime = this._waveElapsedTime + this.waveData.spawnInterval.eval();\r\n\r\n        let spawnOffset = this.waveData.spawnPosOffset;\r\n        let spawnAngle = this.waveData.spawnAngle.eval();\r\n\r\n        if (this.waveData.spawnOrder === eSpawnOrder.Random) {\r\n            const randomWeight = Wave.random() * this._totalWeight;\r\n            for (const group of this.waveData.spawnGroups) {\r\n                if (randomWeight <= group.selfWeight) {\r\n                    await this.createPlane(group.planeID, spawnOffset, spawnAngle);\r\n                    break;\r\n                }\r\n            }\r\n        } else if (this.waveData.spawnOrder === eSpawnOrder.RandomRepeat) {\r\n            await this.createPlane(this._randomRepeatPlaneID, spawnOffset, spawnAngle);\r\n        } else {\r\n            await this.createPlane(this.waveData.spawnGroups[this._spawnIndex % this.waveData.spawnGroups.length].planeID, spawnOffset, spawnAngle);\r\n        }\r\n    }\r\n\r\n    private async createPlane(planeId: number, offset: Vec2, angle: number) {\r\n        let origin: Vec2 = new Vec2(this._originX, this._originY);\r\n        // if (this._path) {\r\n        //     // 如果有路径的情况下, 选择路径起点作为原点\r\n        //     origin = this._path.points[this._path.startIdx].position;\r\n        // }\r\n\r\n        // 获取阵型数据\r\n        if (this._formationGroup) {\r\n            const point = this._formationGroup.points[this._spawnIndex % this._formationGroup.points.length];\r\n            offset.x += point.x;\r\n            offset.y += point.y;\r\n        }\r\n\r\n        origin.x += offset.x;\r\n        origin.y += offset.y;\r\n\r\n        this._spawnIndex++;\r\n        if (this._createPlaneDelegate) {\r\n            this._createPlaneDelegate(planeId, new Vec2(origin.x, offset.y), angle); // this._path ? offset : origin\r\n            return;\r\n        }\r\n\r\n        // console.log(`createPlane: ${planeId}, ${offset.x}, ${offset.y}, ${angle}`);\r\n        let enemy = await GameIns.enemyManager.addPlane(planeId);\r\n        if (enemy) {\r\n            if (this._path) {\r\n                // 注意: Path已经包含了origin, 因此这里我们只传入origin\r\n                enemy.initPath(origin.x, offset.y, this._path);\r\n            } else {\r\n                enemy.initMove(origin.x, origin.y, angle);\r\n            }\r\n            enemy.PlaneEventRegister(PlaneEventType.Die, this.onEnemyDie.bind(this));\r\n            this._enemyCreated.push(enemy); // 记录下来，方便后面判断是否都死了\r\n        }\r\n    }\r\n\r\n    private onEnemyDie(plane: EnemyPlane) {\r\n        plane.PlaneEventUnRegister(PlaneEventType.Die, this.onEnemyDie.bind(this));\r\n        const index = this._enemyCreated.indexOf(plane);\r\n        if (index !== -1) {\r\n            this._enemyCreated.splice(index, 1);\r\n\r\n            if (this._enemyCreated.length === 0 && this._isSpawnCompleted) {\r\n                this._isAllEnemyDead = true;\r\n            }\r\n        }\r\n    }\r\n\r\n    // 以下几个函数是为了给编辑器预览用\r\n    private _createPlaneDelegate: ((planeId: number, pos: Vec2, angle: number) => void)|null = null;\r\n    public setCreatePlaneDelegate(func: (planeId: number, pos: Vec2, angle: number) => void) {\r\n        this._createPlaneDelegate = func;\r\n    }\r\n\r\n    public setupInEditor() {\r\n        // create node and infos for preview\r\n        let node = this.node.getChildByName('WavePreview');\r\n        if (!node) {\r\n            node = new Node('WavePreview');\r\n            node.parent = this.node;\r\n            node.addComponent('WavePreview');\r\n            node.hideFlags = CCObject.Flags.AllHideMasks;\r\n            node.setPosition(0, 0, 0);\r\n        }\r\n        // const thisPos = this.node.position;\r\n        // node.setWorldPosition(750/2 + thisPos.x, 1334/2 + thisPos.y, 0);\r\n\r\n        let spriteNode = this.node.getChildByName('Visual');\r\n        if (!spriteNode) {\r\n            spriteNode = new Node('Visual');\r\n            spriteNode.parent = this.node;\r\n            spriteNode.hideFlags = CCObject.Flags.AllHideMasks;\r\n            \r\n            if (this.waveData.pathAsset && !this._path) {\r\n                this._path = PathData.fromJSON(this.waveData.pathAsset.json);\r\n            }\r\n            if (this._path) {\r\n                const startPoint = this._path.getStartPoint();\r\n                if (startPoint) {\r\n                    spriteNode.setPosition(startPoint.x, startPoint.y, 0);\r\n                }\r\n            }\r\n        }\r\n\r\n        const sprite = spriteNode.getComponent(Sprite) || spriteNode.addComponent(Sprite);\r\n        if (this.waveIcon) {\r\n            sprite.spriteFrame = this.waveIcon;\r\n        } else {\r\n            // 写死的uuid, 编辑器用, 对应: db://assets/resources/game/level/background/Texture/128.png\r\n            assetManager.loadAny<ImageAsset>('707afdf7-8acc-4d9b-9afb-ed259ef05bdc', \r\n                (err:Error|null, asset: ImageAsset) => {\r\n                    if (err) {\r\n                        console.warn(err);\r\n                        return;\r\n                    }\r\n\r\n                    sprite.spriteFrame = SpriteFrame.createWithImage(asset);\r\n                });\r\n        }            \r\n    }\r\n}"]}