System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, view, _GameConst, _crd, GameConst;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      view = _cc.view;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "86dea8H4flPMosr9fpMflgS", "GameConst", undefined);

      __checkObsolete__(['view', 'Vec3', 'Rect']);

      _GameConst = class _GameConst {
        constructor() {
          this.ColliderDraw = false;
          this.ActionFrameTime = 0.0333;
          this.designWidth = 750;
          // 设计分辨率
          this.designHeight = 1334;
          // 设计分辨率
          this.offsetWidth = 200;
          // 宽屏的时候，宽度最高多显示200像素
          this.VIEWPORT_TOP = (1334 + 120) / 2;
          // 视口顶部位置, 往上增加120偏移, 这个用来激活event
          this.VIEWPORT_LOAD_POS = 1334 + 1334 / 2;
          // 视口加载位置, 往上增加1334/2偏移
          this.BATTLE_VIEW_TOP = this.VIEWPORT_TOP;
          this.BATTLE_VIEW_BOTTOM = -this.VIEWPORT_TOP;
          this.BATTLE_VIEW_LEFT = this.ViewBattleWidth / -2;
          this.BATTLE_VIEW_RIGHT = this.ViewBattleWidth / 2;
        }

        get ViewHeight() {
          return view.getVisibleSize().height;
        }

        get ViewWidth() {
          return view.getVisibleSize().width;
        }

        get ViewTop() {
          return view.getVisibleSize().height / 2;
        }

        get ViewBottom() {
          return view.getVisibleSize().height / -2;
        }

        get ViewBattleWidth() {
          let width = view.getVisibleSize().width;
          return width + this.offsetWidth;
        }

        isPointInBattleView(position) {
          return position.x > this.BATTLE_VIEW_LEFT && position.x < this.BATTLE_VIEW_RIGHT && position.y > this.BATTLE_VIEW_BOTTOM && position.y < this.BATTLE_VIEW_TOP;
        }

        isRectInBattleView(rect) {
          return rect.x > this.BATTLE_VIEW_LEFT && rect.x + rect.width < this.BATTLE_VIEW_RIGHT && rect.y > this.BATTLE_VIEW_BOTTOM && rect.y + rect.height < this.BATTLE_VIEW_TOP;
        }

      };

      _export("GameConst", GameConst = new _GameConst());

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=ad5ab252594726c50fbafe899bfed1727ccfe261.js.map