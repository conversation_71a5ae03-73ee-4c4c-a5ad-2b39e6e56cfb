import { _decorator, Component, Graphics, Node, find, Color, SpriteFrame, ImageAsset, assetManager, Sprite } from 'cc';
import { EDITOR } from 'cc/env';
import { GizmoDrawer, autoRegisterGizmoDrawers } from './GizmoDrawer';

// Import gizmo classes to ensure their decorators are executed
import './EmitterGizmo';

const { ccclass, property, executeInEditMode, menu } = _decorator;

// Scene events interface for editor integration
interface ISceneEvents {
    onNodeAdded?(node: Node, opts?: any): void;
    onNodeRemoved?(node: Node, opts?: any): void;
    onComponentAdded?(comp: Component, opts?: any): void;
    onComponentRemoved?(comp: Component, opts?: any): void;
}

/**
 * Global gizmo manager that handles all gizmo drawing
 * Should be attached to a global node in the scene
 * Implements ISceneEvents for automatic refresh on scene changes
 */
@ccclass('GizmoManager')
@menu('Gizmos/GizmoManager')
@executeInEditMode(true)
export class GizmoManager extends Component implements ISceneEvents {
    
    @property
    public gizmosEnabled: boolean = true;
    
    @property
    public drawInPlayMode: boolean = false;
    
    @property
    public refreshRate: number = 60; // FPS for gizmo updates
    
    @property
    public maxDrawDistance: number = 2000; // Maximum distance to draw gizmos
    
    // Graphics component for drawing
    private graphics: Graphics | null = null;
    
    // Registered gizmo drawers
    private static drawers: Map<string, GizmoDrawer> = new Map();
    
    // Update timer
    private updateTimer: number = 0;
    private updateInterval: number = 1 / 60; // Default 60 FPS

    // Scene change detection
    private lastSceneNodeCount: number = 0;
    private sceneChangeCheckInterval: number = 0.1; // Check every 100ms
    private sceneChangeTimer: number = 0;

    // Singleton instance
    private static instance: GizmoManager | null = null;
    public static get Instance() {
        return GizmoManager.instance;
    }

    // Sprite frame cache for icons
    private static spriteFrameCache: Map<string, SpriteFrame | null> = new Map();
    private static loadingPromises: Map<string, Promise<SpriteFrame | null>> = new Map();
    
    protected onLoad(): void {
        if (!EDITOR && !this.drawInPlayMode) return;

        // Set as singleton instance
        GizmoManager.instance = this;

        // Ensure the gizmo manager node is positioned at origin for proper coordinate mapping
        this.node.setPosition(0, 0, 0);
        this.node.setRotation(0, 0, 0, 1);
        this.node.setScale(1, 1, 1);

        // Get or create Graphics component
        this.graphics = this.getComponent(Graphics) || this.addComponent(Graphics);

        // Update refresh interval
        this.updateInterval = 1 / this.refreshRate;

        // Auto-register all decorated gizmo drawers
        GizmoManager.autoRegisterDrawers();

        // Initialize scene change detection
        const rootNodes = this.findAllRootNodes();
        this.lastSceneNodeCount = this.countAllNodesRecursive(rootNodes);

        // Register for editor scene events if available
        this.registerSceneEvents();
        this.registerSelectionEvents();
    }
    
    protected onDestroy(): void {
        if (GizmoManager.instance === this) {
            GizmoManager.instance = null;
        }

        // Unregister scene events
        this.unregisterSceneEvents();
    }
    
    protected update(deltaTime: number): void {
        if (!EDITOR && !this.drawInPlayMode) return;
        if (!this.graphics) return;

        // Check for scene changes periodically
        this.sceneChangeTimer += deltaTime;
        let forceRedraw = false;
        if (this.sceneChangeTimer >= this.sceneChangeCheckInterval) {
            this.sceneChangeTimer = 0;
            forceRedraw = this.checkForSceneChanges();
        }

        // Throttle updates based on refresh rate (unless forced by scene change)
        this.updateTimer += deltaTime;
        if (!forceRedraw && this.updateTimer < this.updateInterval) return;
        this.updateTimer = 0;

        if (this.gizmosEnabled) {
            this.drawAllGizmos();
        } else {
            // Clear graphics when gizmos are disabled
            this.graphics.clear();
        }
    }
    
    /**
     * Draw all gizmos for all registered drawers
     */
    private drawAllGizmos(): void {
        if (!this.graphics)
            return;

        // Clear previous drawings
        this.graphics.clear();

        // Get all drawers sorted by priority
        const sortedDrawers = Array.from(GizmoManager.drawers.values())
            .filter(drawer => drawer.enabled)
            .sort((a, b) => a.getPriority() - b.getPriority());

        // Find all nodes in the scene
        const rootNodes = this.findAllRootNodes();

        // Draw gizmos for each drawer
        for (const drawer of sortedDrawers) {
            this.drawGizmosForDrawer(drawer, rootNodes);
        }
    }
    
    /**
     * Draw gizmos for a specific drawer
     */
    private drawGizmosForDrawer(drawer: GizmoDrawer, rootNodes: Node[]): void {
        if (!this.graphics) return;

        const componentsToProcess: { component: Component, node: Node }[] = [];

        // Find all components of the drawer's type
        for (const rootNode of rootNodes) {
            this.findComponentsRecursive(rootNode, drawer, componentsToProcess);
        }

        // Draw gizmos for each component
        for (const { component, node } of componentsToProcess) {
            try {
                drawer.drawGizmos(component, this.graphics, node);
            } catch (error) {
                console.error(`GizmoManager: Error drawing gizmos for ${drawer.drawerName}:`, error);
            }
        }
    }
    
    /**
     * Recursively find components that match the drawer's type
     */
    private findComponentsRecursive(node: Node, drawer: GizmoDrawer, results: { component: Component, node: Node }[]): void {
        // Check distance from gizmo manager
        const distance = node.worldPosition.subtract(this.node.worldPosition).length();
        if (distance > this.maxDrawDistance) return;
        
        // Check components on this node
        const components = node.getComponents(Component);
        for (const component of components) {
            if (drawer.canHandle(component)) {
                results.push({ component, node });
            }
        }
        
        // Recursively check children
        for (const child of node.children) {
            this.findComponentsRecursive(child, drawer, results);
        }
    }
    
    /**
     * Check if the scene has changed (nodes added/removed)
     * Returns true if a redraw should be forced
     */
    private checkForSceneChanges(): boolean {
        const rootNodes = this.findAllRootNodes();
        const currentNodeCount = this.countAllNodesRecursive(rootNodes);

        if (currentNodeCount !== this.lastSceneNodeCount) {
            this.lastSceneNodeCount = currentNodeCount;
            return true; // Force redraw
        }

        return false;
    }

    /**
     * Count all nodes recursively in the scene
     */
    private countAllNodesRecursive(nodes: Node[]): number {
        let count = nodes.length;
        for (const node of nodes) {
            count += this.countAllNodesRecursive(node.children);
        }
        return count;
    }

    /**
     * Find all root nodes in the scene
     */
    private findAllRootNodes(): Node[] {
        const scene = this.node.scene;
        if (!scene) return [];

        return scene.children.filter(child => child !== this.node);
    }
    
    /**
     * Register a gizmo drawer
     */
    public static registerDrawer(drawer: GizmoDrawer): void {
        const key = drawer.drawerName;
        
        if (GizmoManager.drawers.has(key)) {
            console.warn(`GizmoManager: Drawer ${key} is already registered`);
            return;
        }
        
        GizmoManager.drawers.set(key, drawer);
        drawer.onRegister();
        
        if (EDITOR) {
            console.log(`GizmoManager: Registered drawer ${key}`);
        }
    }
    
    /**
     * Unregister a gizmo drawer
     */
    public static unregisterDrawer(drawerName: string): boolean {
        const drawer = GizmoManager.drawers.get(drawerName);
        if (!drawer) return false;
        
        drawer.onUnregister();
        GizmoManager.drawers.delete(drawerName);
        
        if (EDITOR) {
            console.log(`GizmoManager: Unregistered drawer ${drawerName}`);
        }
        
        return true;
    }
    
    /**
     * Get a registered drawer by name
     */
    public static getDrawer(drawerName: string): GizmoDrawer | null {
        return GizmoManager.drawers.get(drawerName) || null;
    }
    
    /**
     * Get all registered drawers
     */
    public static getAllDrawers(): GizmoDrawer[] {
        return Array.from(GizmoManager.drawers.values());
    }
    
    /**
     * Get the singleton instance
     */
    public static getInstance(): GizmoManager | null {
        return GizmoManager.instance;
    }
    
    /**
     * Auto-register all decorated gizmo drawers
     */
    public static autoRegisterDrawers(): void {
        autoRegisterGizmoDrawers((drawer: GizmoDrawer) => {
            GizmoManager.registerDrawer(drawer);
        });
    }

    /**
     * Force refresh all gizmos and reset scene change detection
     */
    public static refresh(): void {
        const instance = GizmoManager.getInstance();
        if (instance) {
            // Reset scene change detection to force immediate update
            instance.lastSceneNodeCount = -1;
            instance.drawAllGizmos();
        }
    }

    /**
     * Force immediate gizmo refresh (useful after scene modifications)
     */
    public static forceRefresh(): void {
        const instance = GizmoManager.getInstance();
        if (instance) {
            // Update node count and force redraw
            const rootNodes = instance.findAllRootNodes();
            instance.lastSceneNodeCount = instance.countAllNodesRecursive(rootNodes);
            instance.drawAllGizmos();
        }
    }

    // ========== Scene Events Implementation ==========

    /**
     * Register for editor scene events
     */
    private registerSceneEvents(): void {
        if (!EDITOR) return;

        try {
            // Try to register with the editor's scene event system
            // This is a best-effort approach as the API may vary
            const editorExtends = (globalThis as any).EditorExtends;
            if (editorExtends && editorExtends.on) {
                editorExtends.on('scene:node-added', this.onNodeAdded.bind(this));
                editorExtends.on('scene:node-removed', this.onNodeRemoved.bind(this));
                editorExtends.on('scene:component-added', this.onComponentAdded.bind(this));
                editorExtends.on('scene:component-removed', this.onComponentRemoved.bind(this));
            }

        } catch (error) {
            // Silent fallback - scene events not available
        }
    }

    /**
     * Unregister scene events
     */
    private unregisterSceneEvents(): void {
        if (!EDITOR) return;

        try {
            const editorExtends = (globalThis as any).EditorExtends;
            if (editorExtends && editorExtends.removeListener) {
                editorExtends.removeListener('scene:node-added', this.onNodeAdded.bind(this));
                editorExtends.removeListener('scene:node-removed', this.onNodeRemoved.bind(this));
                editorExtends.removeListener('scene:component-added', this.onComponentAdded.bind(this));
                editorExtends.removeListener('scene:component-removed', this.onComponentRemoved.bind(this));
            }
        } catch (error) {
            // Silent fallback
        }
    }

    private registerSelectionEvents(): void {
        if (!EDITOR) return;
        
        // 这个暂时没法注册成功, 估计需要
        // try {
        //     // @ts-ignore
        //     Editor.Ipc.addListener('_selection:selected', (event, uuids) => {
        //         console.log('Selection changed:', uuids);
        //     });

        // } catch (error) {
        //     console.warn('GizmoManager: Failed to register selection events', error);
        // }
    }
        

    /**
     * Called when a node is added to the scene
     */
    public onNodeAdded(_node: Node, _opts?: any): void {
        // Force immediate refresh when nodes are added
        this.lastSceneNodeCount = -1; // Force count update
        GizmoManager.forceRefresh();
    }

    /**
     * Called when a node is removed from the scene
     */
    public onNodeRemoved(_node: Node, _opts?: any): void {
        // Force immediate refresh when nodes are removed
        this.lastSceneNodeCount = -1; // Force count update
        GizmoManager.forceRefresh();
    }

    /**
     * Called when a component is added to a node
     */
    public onComponentAdded(comp: Component, _opts?: any): void {
        // Check if it's a component type we care about
        for (const drawer of GizmoManager.drawers.values()) {
            if (drawer.canHandle(comp)) {
                // This is a component we draw gizmos for, force refresh
                GizmoManager.forceRefresh();
                break;
            }
        }
    }

    /**
     * Called when a component is removed from a node
     */
    public onComponentRemoved(comp: Component, _opts?: any): void {
        // Check if it's a component type we care about
        for (const drawer of GizmoManager.drawers.values()) {
            if (drawer.canHandle(comp)) {
                // This is a component we draw gizmos for, force refresh
                GizmoManager.forceRefresh();
                break;
            }
        }
    }

    /**
     * Load sprite frame with caching
     */
    public static async loadSpriteFrame(iconPath: string): Promise<SpriteFrame | null> {
        // Check cache first
        if (GizmoManager.spriteFrameCache.has(iconPath)) {
            return GizmoManager.spriteFrameCache.get(iconPath) || null;
        }

        // Check if already loading
        if (GizmoManager.loadingPromises.has(iconPath)) {
            return GizmoManager.loadingPromises.get(iconPath) || null;
        }

        // Start loading
        const loadingPromise = GizmoManager.loadSpriteFrameInternal(iconPath);
        GizmoManager.loadingPromises.set(iconPath, loadingPromise);

        const result = await loadingPromise;

        // Cache the result and remove from loading promises
        GizmoManager.spriteFrameCache.set(iconPath, result);
        GizmoManager.loadingPromises.delete(iconPath);

        return result;
    }

    /**
     * Internal method to load sprite frame
     */
    private static async loadSpriteFrameInternal(iconPath: string): Promise<SpriteFrame | null> {
        try {
            const fullPath = `db://assets/editor/icons/${iconPath}`;
            // @ts-ignore
            const uuid = await Editor.Message.request('asset-db', 'query-uuid', fullPath);
            if (!uuid) {
                console.warn(`Failed to query uuid for icon: ${fullPath}`);
                return null;
            }

            return new Promise<SpriteFrame | null>((resolve) => {
                assetManager.loadAny<ImageAsset>(uuid, (err:Error|null, asset: ImageAsset) => {
                    if (err) {
                        console.warn(`Failed to load icon: ${fullPath}`, err);
                        resolve(null);
                        return;
                    }

                    const spriteFrame = SpriteFrame.createWithImage(asset);
                    resolve(spriteFrame);
                });
            });
        } catch (error) {
            console.warn(`Failed to load icon: ${iconPath}`, error);
            return null;
        }
    }

    /**
     * Load and apply sprite frame to a sprite component
     */
    public static async loadAndApplySpriteFrame(iconPath: string, sprite: Sprite): Promise<void> {
        try {
            const spriteFrame = await GizmoManager.loadSpriteFrame(iconPath);

            // Apply to sprite if still valid
            if (sprite && sprite.isValid && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
            }
        } catch (error) {
            console.warn(`Failed to load and apply sprite frame for ${iconPath}:`, error);
        }
    }

    /**
     * Clear sprite frame cache
     */
    public static clearSpriteFrameCache(): void {
        GizmoManager.spriteFrameCache.clear();
        GizmoManager.loadingPromises.clear();
    }
}
