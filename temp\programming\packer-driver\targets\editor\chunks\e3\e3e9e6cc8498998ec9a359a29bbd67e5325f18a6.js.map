{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/gizmos/GizmoManager.ts"], "names": ["_decorator", "Component", "Graphics", "SpriteFrame", "assetManager", "EDITOR", "autoRegisterGizmoDrawers", "ccclass", "property", "executeInEditMode", "menu", "GizmoManager", "graphics", "updateTimer", "updateInterval", "lastSceneNodeCount", "sceneChangeCheckInterval", "sceneChangeTimer", "Instance", "instance", "onLoad", "drawInPlayMode", "node", "setPosition", "setRotation", "setScale", "getComponent", "addComponent", "refreshRate", "autoRegisterDrawers", "rootNodes", "findAllRootNodes", "countAllNodesRecursive", "registerSceneEvents", "registerSelectionEvents", "onDestroy", "unregisterSceneEvents", "update", "deltaTime", "forceRedraw", "checkForSceneChanges", "gizmosEnabled", "drawAllGizmos", "clear", "sortedDrawers", "Array", "from", "drawers", "values", "filter", "drawer", "enabled", "sort", "a", "b", "getPriority", "drawGizmosForDrawer", "componentsToProcess", "rootNode", "findComponentsRecursive", "component", "drawGizmos", "error", "console", "drawerName", "results", "distance", "worldPosition", "subtract", "length", "maxDrawDistance", "components", "getComponents", "canHandle", "push", "child", "children", "currentNodeCount", "nodes", "count", "scene", "registerDrawer", "key", "has", "warn", "set", "onRegister", "log", "unregisterDrawer", "get", "onUnregister", "delete", "get<PERSON>rawer", "getAllDrawers", "getInstance", "refresh", "forceRefresh", "editorExtends", "globalThis", "EditorExtends", "on", "onNodeAdded", "bind", "onNodeRemoved", "onComponentAdded", "onComponentRemoved", "removeListener", "_node", "_opts", "comp", "loadSpriteFrame", "iconPath", "spriteFrameCache", "loadingPromises", "loadingPromise", "loadSpriteFrameInternal", "result", "fullPath", "uuid", "Editor", "Message", "request", "Promise", "resolve", "loadAny", "err", "asset", "spriteFrame", "createWithImage", "loadAndApplySpriteFrame", "sprite", "<PERSON><PERSON><PERSON><PERSON>", "clearSpriteFrameCache", "Map"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,Q,OAAAA,Q;AAA6BC,MAAAA,W,OAAAA,W;AAAyBC,MAAAA,Y,OAAAA,Y;;AAC7EC,MAAAA,M,UAAAA,M;;AACaC,MAAAA,wB,iBAAAA,wB;;;;;;;kJAEtB;;;OAGM;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA,iBAArB;AAAwCC,QAAAA;AAAxC,O,GAAiDV,U,GAEvD;;AAQA;AACA;AACA;AACA;AACA;8BAIaW,Y,WAHZJ,OAAO,CAAC,cAAD,C,UACPG,IAAI,CAAC,qBAAD,C,UACJD,iBAAiB,CAAC,IAAD,C,oEAFlB,MAGaE,YAHb,SAGkCV,SAHlC,CAGoE;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAS/B;AAT+B;;AAYzB;AAEvC;AAdgE,eAexDW,QAfwD,GAe5B,IAf4B;AAoBhE;AApBgE,eAqBxDC,WArBwD,GAqBlC,CArBkC;AAAA,eAsBxDC,cAtBwD,GAsB/B,IAAI,EAtB2B;AAsBvB;AAEzC;AAxBgE,eAyBxDC,kBAzBwD,GAyB3B,CAzB2B;AAAA,eA0BxDC,wBA1BwD,GA0BrB,GA1BqB;AA0BhB;AA1BgB,eA2BxDC,gBA3BwD,GA2B7B,CA3B6B;AAAA;;AA+BtC,mBAARC,QAAQ,GAAG;AACzB,iBAAOP,YAAY,CAACQ,QAApB;AACH,SAjC+D,CAmChE;;;AAIUC,QAAAA,MAAM,GAAS;AACrB,cAAI,CAACf,MAAD,IAAW,CAAC,KAAKgB,cAArB,EAAqC,OADhB,CAGrB;;AACAV,UAAAA,YAAY,CAACQ,QAAb,GAAwB,IAAxB,CAJqB,CAMrB;;AACA,eAAKG,IAAL,CAAUC,WAAV,CAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B;AACA,eAAKD,IAAL,CAAUE,WAAV,CAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B,EAA+B,CAA/B;AACA,eAAKF,IAAL,CAAUG,QAAV,CAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB,EATqB,CAWrB;;AACA,eAAKb,QAAL,GAAgB,KAAKc,YAAL,CAAkBxB,QAAlB,KAA+B,KAAKyB,YAAL,CAAkBzB,QAAlB,CAA/C,CAZqB,CAcrB;;AACA,eAAKY,cAAL,GAAsB,IAAI,KAAKc,WAA/B,CAfqB,CAiBrB;;AACAjB,UAAAA,YAAY,CAACkB,mBAAb,GAlBqB,CAoBrB;;AACA,gBAAMC,SAAS,GAAG,KAAKC,gBAAL,EAAlB;AACA,eAAKhB,kBAAL,GAA0B,KAAKiB,sBAAL,CAA4BF,SAA5B,CAA1B,CAtBqB,CAwBrB;;AACA,eAAKG,mBAAL;AACA,eAAKC,uBAAL;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB,cAAIxB,YAAY,CAACQ,QAAb,KAA0B,IAA9B,EAAoC;AAChCR,YAAAA,YAAY,CAACQ,QAAb,GAAwB,IAAxB;AACH,WAHuB,CAKxB;;;AACA,eAAKiB,qBAAL;AACH;;AAESC,QAAAA,MAAM,CAACC,SAAD,EAA0B;AACtC,cAAI,CAACjC,MAAD,IAAW,CAAC,KAAKgB,cAArB,EAAqC;AACrC,cAAI,CAAC,KAAKT,QAAV,EAAoB,OAFkB,CAItC;;AACA,eAAKK,gBAAL,IAAyBqB,SAAzB;AACA,cAAIC,WAAW,GAAG,KAAlB;;AACA,cAAI,KAAKtB,gBAAL,IAAyB,KAAKD,wBAAlC,EAA4D;AACxD,iBAAKC,gBAAL,GAAwB,CAAxB;AACAsB,YAAAA,WAAW,GAAG,KAAKC,oBAAL,EAAd;AACH,WAVqC,CAYtC;;;AACA,eAAK3B,WAAL,IAAoByB,SAApB;AACA,cAAI,CAACC,WAAD,IAAgB,KAAK1B,WAAL,GAAmB,KAAKC,cAA5C,EAA4D;AAC5D,eAAKD,WAAL,GAAmB,CAAnB;;AAEA,cAAI,KAAK4B,aAAT,EAAwB;AACpB,iBAAKC,aAAL;AACH,WAFD,MAEO;AACH;AACA,iBAAK9B,QAAL,CAAc+B,KAAd;AACH;AACJ;AAED;AACJ;AACA;;;AACYD,QAAAA,aAAa,GAAS;AAC1B,cAAI,CAAC,KAAK9B,QAAV,EACI,OAFsB,CAI1B;;AACA,eAAKA,QAAL,CAAc+B,KAAd,GAL0B,CAO1B;;AACA,gBAAMC,aAAa,GAAGC,KAAK,CAACC,IAAN,CAAWnC,YAAY,CAACoC,OAAb,CAAqBC,MAArB,EAAX,EACjBC,MADiB,CACVC,MAAM,IAAIA,MAAM,CAACC,OADP,EAEjBC,IAFiB,CAEZ,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,CAACE,WAAF,KAAkBD,CAAC,CAACC,WAAF,EAFhB,CAAtB,CAR0B,CAY1B;;AACA,gBAAMzB,SAAS,GAAG,KAAKC,gBAAL,EAAlB,CAb0B,CAe1B;;AACA,eAAK,MAAMmB,MAAX,IAAqBN,aAArB,EAAoC;AAChC,iBAAKY,mBAAL,CAAyBN,MAAzB,EAAiCpB,SAAjC;AACH;AACJ;AAED;AACJ;AACA;;;AACY0B,QAAAA,mBAAmB,CAACN,MAAD,EAAsBpB,SAAtB,EAA+C;AACtE,cAAI,CAAC,KAAKlB,QAAV,EAAoB;AAEpB,gBAAM6C,mBAA2D,GAAG,EAApE,CAHsE,CAKtE;;AACA,eAAK,MAAMC,QAAX,IAAuB5B,SAAvB,EAAkC;AAC9B,iBAAK6B,uBAAL,CAA6BD,QAA7B,EAAuCR,MAAvC,EAA+CO,mBAA/C;AACH,WARqE,CAUtE;;;AACA,eAAK,MAAM;AAAEG,YAAAA,SAAF;AAAatC,YAAAA;AAAb,WAAX,IAAkCmC,mBAAlC,EAAuD;AACnD,gBAAI;AACAP,cAAAA,MAAM,CAACW,UAAP,CAAkBD,SAAlB,EAA6B,KAAKhD,QAAlC,EAA4CU,IAA5C;AACH,aAFD,CAEE,OAAOwC,KAAP,EAAc;AACZC,cAAAA,OAAO,CAACD,KAAR,CAAe,0CAAyCZ,MAAM,CAACc,UAAW,GAA1E,EAA8EF,KAA9E;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACYH,QAAAA,uBAAuB,CAACrC,IAAD,EAAa4B,MAAb,EAAkCe,OAAlC,EAAyF;AACpH;AACA,gBAAMC,QAAQ,GAAG5C,IAAI,CAAC6C,aAAL,CAAmBC,QAAnB,CAA4B,KAAK9C,IAAL,CAAU6C,aAAtC,EAAqDE,MAArD,EAAjB;AACA,cAAIH,QAAQ,GAAG,KAAKI,eAApB,EAAqC,OAH+E,CAKpH;;AACA,gBAAMC,UAAU,GAAGjD,IAAI,CAACkD,aAAL,CAAmBvE,SAAnB,CAAnB;;AACA,eAAK,MAAM2D,SAAX,IAAwBW,UAAxB,EAAoC;AAChC,gBAAIrB,MAAM,CAACuB,SAAP,CAAiBb,SAAjB,CAAJ,EAAiC;AAC7BK,cAAAA,OAAO,CAACS,IAAR,CAAa;AAAEd,gBAAAA,SAAF;AAAatC,gBAAAA;AAAb,eAAb;AACH;AACJ,WAXmH,CAapH;;;AACA,eAAK,MAAMqD,KAAX,IAAoBrD,IAAI,CAACsD,QAAzB,EAAmC;AAC/B,iBAAKjB,uBAAL,CAA6BgB,KAA7B,EAAoCzB,MAApC,EAA4Ce,OAA5C;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACYzB,QAAAA,oBAAoB,GAAY;AACpC,gBAAMV,SAAS,GAAG,KAAKC,gBAAL,EAAlB;AACA,gBAAM8C,gBAAgB,GAAG,KAAK7C,sBAAL,CAA4BF,SAA5B,CAAzB;;AAEA,cAAI+C,gBAAgB,KAAK,KAAK9D,kBAA9B,EAAkD;AAC9C,iBAAKA,kBAAL,GAA0B8D,gBAA1B;AACA,mBAAO,IAAP,CAF8C,CAEjC;AAChB;;AAED,iBAAO,KAAP;AACH;AAED;AACJ;AACA;;;AACY7C,QAAAA,sBAAsB,CAAC8C,KAAD,EAAwB;AAClD,cAAIC,KAAK,GAAGD,KAAK,CAACT,MAAlB;;AACA,eAAK,MAAM/C,IAAX,IAAmBwD,KAAnB,EAA0B;AACtBC,YAAAA,KAAK,IAAI,KAAK/C,sBAAL,CAA4BV,IAAI,CAACsD,QAAjC,CAAT;AACH;;AACD,iBAAOG,KAAP;AACH;AAED;AACJ;AACA;;;AACYhD,QAAAA,gBAAgB,GAAW;AAC/B,gBAAMiD,KAAK,GAAG,KAAK1D,IAAL,CAAU0D,KAAxB;AACA,cAAI,CAACA,KAAL,EAAY,OAAO,EAAP;AAEZ,iBAAOA,KAAK,CAACJ,QAAN,CAAe3B,MAAf,CAAsB0B,KAAK,IAAIA,KAAK,KAAK,KAAKrD,IAA9C,CAAP;AACH;AAED;AACJ;AACA;;;AACgC,eAAd2D,cAAc,CAAC/B,MAAD,EAA4B;AACpD,gBAAMgC,GAAG,GAAGhC,MAAM,CAACc,UAAnB;;AAEA,cAAIrD,YAAY,CAACoC,OAAb,CAAqBoC,GAArB,CAAyBD,GAAzB,CAAJ,EAAmC;AAC/BnB,YAAAA,OAAO,CAACqB,IAAR,CAAc,wBAAuBF,GAAI,wBAAzC;AACA;AACH;;AAEDvE,UAAAA,YAAY,CAACoC,OAAb,CAAqBsC,GAArB,CAAyBH,GAAzB,EAA8BhC,MAA9B;AACAA,UAAAA,MAAM,CAACoC,UAAP;;AAEA,cAAIjF,MAAJ,EAAY;AACR0D,YAAAA,OAAO,CAACwB,GAAR,CAAa,mCAAkCL,GAAI,EAAnD;AACH;AACJ;AAED;AACJ;AACA;;;AACkC,eAAhBM,gBAAgB,CAACxB,UAAD,EAA8B;AACxD,gBAAMd,MAAM,GAAGvC,YAAY,CAACoC,OAAb,CAAqB0C,GAArB,CAAyBzB,UAAzB,CAAf;AACA,cAAI,CAACd,MAAL,EAAa,OAAO,KAAP;AAEbA,UAAAA,MAAM,CAACwC,YAAP;AACA/E,UAAAA,YAAY,CAACoC,OAAb,CAAqB4C,MAArB,CAA4B3B,UAA5B;;AAEA,cAAI3D,MAAJ,EAAY;AACR0D,YAAAA,OAAO,CAACwB,GAAR,CAAa,qCAAoCvB,UAAW,EAA5D;AACH;;AAED,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AAC2B,eAAT4B,SAAS,CAAC5B,UAAD,EAAyC;AAC5D,iBAAOrD,YAAY,CAACoC,OAAb,CAAqB0C,GAArB,CAAyBzB,UAAzB,KAAwC,IAA/C;AACH;AAED;AACJ;AACA;;;AAC+B,eAAb6B,aAAa,GAAkB;AACzC,iBAAOhD,KAAK,CAACC,IAAN,CAAWnC,YAAY,CAACoC,OAAb,CAAqBC,MAArB,EAAX,CAAP;AACH;AAED;AACJ;AACA;;;AAC6B,eAAX8C,WAAW,GAAwB;AAC7C,iBAAOnF,YAAY,CAACQ,QAApB;AACH;AAED;AACJ;AACA;;;AACqC,eAAnBU,mBAAmB,GAAS;AACtC;AAAA;AAAA,oEAA0BqB,MAAD,IAAyB;AAC9CvC,YAAAA,YAAY,CAACsE,cAAb,CAA4B/B,MAA5B;AACH,WAFD;AAGH;AAED;AACJ;AACA;;;AACyB,eAAP6C,OAAO,GAAS;AAC1B,gBAAM5E,QAAQ,GAAGR,YAAY,CAACmF,WAAb,EAAjB;;AACA,cAAI3E,QAAJ,EAAc;AACV;AACAA,YAAAA,QAAQ,CAACJ,kBAAT,GAA8B,CAAC,CAA/B;AACAI,YAAAA,QAAQ,CAACuB,aAAT;AACH;AACJ;AAED;AACJ;AACA;;;AAC8B,eAAZsD,YAAY,GAAS;AAC/B,gBAAM7E,QAAQ,GAAGR,YAAY,CAACmF,WAAb,EAAjB;;AACA,cAAI3E,QAAJ,EAAc;AACV;AACA,kBAAMW,SAAS,GAAGX,QAAQ,CAACY,gBAAT,EAAlB;AACAZ,YAAAA,QAAQ,CAACJ,kBAAT,GAA8BI,QAAQ,CAACa,sBAAT,CAAgCF,SAAhC,CAA9B;AACAX,YAAAA,QAAQ,CAACuB,aAAT;AACH;AACJ,SAzS+D,CA2ShE;;AAEA;AACJ;AACA;;;AACYT,QAAAA,mBAAmB,GAAS;AAChC,cAAI,CAAC5B,MAAL,EAAa;;AAEb,cAAI;AACA;AACA;AACA,kBAAM4F,aAAa,GAAIC,UAAD,CAAoBC,aAA1C;;AACA,gBAAIF,aAAa,IAAIA,aAAa,CAACG,EAAnC,EAAuC;AACnCH,cAAAA,aAAa,CAACG,EAAd,CAAiB,kBAAjB,EAAqC,KAAKC,WAAL,CAAiBC,IAAjB,CAAsB,IAAtB,CAArC;AACAL,cAAAA,aAAa,CAACG,EAAd,CAAiB,oBAAjB,EAAuC,KAAKG,aAAL,CAAmBD,IAAnB,CAAwB,IAAxB,CAAvC;AACAL,cAAAA,aAAa,CAACG,EAAd,CAAiB,uBAAjB,EAA0C,KAAKI,gBAAL,CAAsBF,IAAtB,CAA2B,IAA3B,CAA1C;AACAL,cAAAA,aAAa,CAACG,EAAd,CAAiB,yBAAjB,EAA4C,KAAKK,kBAAL,CAAwBH,IAAxB,CAA6B,IAA7B,CAA5C;AACH;AAEJ,WAXD,CAWE,OAAOxC,KAAP,EAAc,CACZ;AACH;AACJ;AAED;AACJ;AACA;;;AACY1B,QAAAA,qBAAqB,GAAS;AAClC,cAAI,CAAC/B,MAAL,EAAa;;AAEb,cAAI;AACA,kBAAM4F,aAAa,GAAIC,UAAD,CAAoBC,aAA1C;;AACA,gBAAIF,aAAa,IAAIA,aAAa,CAACS,cAAnC,EAAmD;AAC/CT,cAAAA,aAAa,CAACS,cAAd,CAA6B,kBAA7B,EAAiD,KAAKL,WAAL,CAAiBC,IAAjB,CAAsB,IAAtB,CAAjD;AACAL,cAAAA,aAAa,CAACS,cAAd,CAA6B,oBAA7B,EAAmD,KAAKH,aAAL,CAAmBD,IAAnB,CAAwB,IAAxB,CAAnD;AACAL,cAAAA,aAAa,CAACS,cAAd,CAA6B,uBAA7B,EAAsD,KAAKF,gBAAL,CAAsBF,IAAtB,CAA2B,IAA3B,CAAtD;AACAL,cAAAA,aAAa,CAACS,cAAd,CAA6B,yBAA7B,EAAwD,KAAKD,kBAAL,CAAwBH,IAAxB,CAA6B,IAA7B,CAAxD;AACH;AACJ,WARD,CAQE,OAAOxC,KAAP,EAAc,CACZ;AACH;AACJ;;AAEO5B,QAAAA,uBAAuB,GAAS;AACpC,cAAI,CAAC7B,MAAL,EAAa,OADuB,CAGpC;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACH;AAGD;AACJ;AACA;;;AACWgG,QAAAA,WAAW,CAACM,KAAD,EAAcC,KAAd,EAAiC;AAC/C;AACA,eAAK7F,kBAAL,GAA0B,CAAC,CAA3B,CAF+C,CAEjB;;AAC9BJ,UAAAA,YAAY,CAACqF,YAAb;AACH;AAED;AACJ;AACA;;;AACWO,QAAAA,aAAa,CAACI,KAAD,EAAcC,KAAd,EAAiC;AACjD;AACA,eAAK7F,kBAAL,GAA0B,CAAC,CAA3B,CAFiD,CAEnB;;AAC9BJ,UAAAA,YAAY,CAACqF,YAAb;AACH;AAED;AACJ;AACA;;;AACWQ,QAAAA,gBAAgB,CAACK,IAAD,EAAkBD,KAAlB,EAAqC;AACxD;AACA,eAAK,MAAM1D,MAAX,IAAqBvC,YAAY,CAACoC,OAAb,CAAqBC,MAArB,EAArB,EAAoD;AAChD,gBAAIE,MAAM,CAACuB,SAAP,CAAiBoC,IAAjB,CAAJ,EAA4B;AACxB;AACAlG,cAAAA,YAAY,CAACqF,YAAb;AACA;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACWS,QAAAA,kBAAkB,CAACI,IAAD,EAAkBD,KAAlB,EAAqC;AAC1D;AACA,eAAK,MAAM1D,MAAX,IAAqBvC,YAAY,CAACoC,OAAb,CAAqBC,MAArB,EAArB,EAAoD;AAChD,gBAAIE,MAAM,CAACuB,SAAP,CAAiBoC,IAAjB,CAAJ,EAA4B;AACxB;AACAlG,cAAAA,YAAY,CAACqF,YAAb;AACA;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACuC,qBAAfc,eAAe,CAACC,QAAD,EAAgD;AAC/E;AACA,cAAIpG,YAAY,CAACqG,gBAAb,CAA8B7B,GAA9B,CAAkC4B,QAAlC,CAAJ,EAAiD;AAC7C,mBAAOpG,YAAY,CAACqG,gBAAb,CAA8BvB,GAA9B,CAAkCsB,QAAlC,KAA+C,IAAtD;AACH,WAJ8E,CAM/E;;;AACA,cAAIpG,YAAY,CAACsG,eAAb,CAA6B9B,GAA7B,CAAiC4B,QAAjC,CAAJ,EAAgD;AAC5C,mBAAOpG,YAAY,CAACsG,eAAb,CAA6BxB,GAA7B,CAAiCsB,QAAjC,KAA8C,IAArD;AACH,WAT8E,CAW/E;;;AACA,gBAAMG,cAAc,GAAGvG,YAAY,CAACwG,uBAAb,CAAqCJ,QAArC,CAAvB;AACApG,UAAAA,YAAY,CAACsG,eAAb,CAA6B5B,GAA7B,CAAiC0B,QAAjC,EAA2CG,cAA3C;AAEA,gBAAME,MAAM,GAAG,MAAMF,cAArB,CAf+E,CAiB/E;;AACAvG,UAAAA,YAAY,CAACqG,gBAAb,CAA8B3B,GAA9B,CAAkC0B,QAAlC,EAA4CK,MAA5C;AACAzG,UAAAA,YAAY,CAACsG,eAAb,CAA6BtB,MAA7B,CAAoCoB,QAApC;AAEA,iBAAOK,MAAP;AACH;AAED;AACJ;AACA;;;AACgD,qBAAvBD,uBAAuB,CAACJ,QAAD,EAAgD;AACxF,cAAI;AACA,kBAAMM,QAAQ,GAAI,4BAA2BN,QAAS,EAAtD,CADA,CAEA;;AACA,kBAAMO,IAAI,GAAG,MAAMC,MAAM,CAACC,OAAP,CAAeC,OAAf,CAAuB,UAAvB,EAAmC,YAAnC,EAAiDJ,QAAjD,CAAnB;;AACA,gBAAI,CAACC,IAAL,EAAW;AACPvD,cAAAA,OAAO,CAACqB,IAAR,CAAc,kCAAiCiC,QAAS,EAAxD;AACA,qBAAO,IAAP;AACH;;AAED,mBAAO,IAAIK,OAAJ,CAAiCC,OAAD,IAAa;AAChDvH,cAAAA,YAAY,CAACwH,OAAb,CAAiCN,IAAjC,EAAuC,CAACO,GAAD,EAAiBC,KAAjB,KAAuC;AAC1E,oBAAID,GAAJ,EAAS;AACL9D,kBAAAA,OAAO,CAACqB,IAAR,CAAc,wBAAuBiC,QAAS,EAA9C,EAAiDQ,GAAjD;AACAF,kBAAAA,OAAO,CAAC,IAAD,CAAP;AACA;AACH;;AAED,sBAAMI,WAAW,GAAG5H,WAAW,CAAC6H,eAAZ,CAA4BF,KAA5B,CAApB;AACAH,gBAAAA,OAAO,CAACI,WAAD,CAAP;AACH,eATD;AAUH,aAXM,CAAP;AAYH,WArBD,CAqBE,OAAOjE,KAAP,EAAc;AACZC,YAAAA,OAAO,CAACqB,IAAR,CAAc,wBAAuB2B,QAAS,EAA9C,EAAiDjD,KAAjD;AACA,mBAAO,IAAP;AACH;AACJ;AAED;AACJ;AACA;;;AAC+C,qBAAvBmE,uBAAuB,CAAClB,QAAD,EAAmBmB,MAAnB,EAAkD;AACzF,cAAI;AACA,kBAAMH,WAAW,GAAG,MAAMpH,YAAY,CAACmG,eAAb,CAA6BC,QAA7B,CAA1B,CADA,CAGA;;AACA,gBAAImB,MAAM,IAAIA,MAAM,CAACC,OAAjB,IAA4BJ,WAAhC,EAA6C;AACzCG,cAAAA,MAAM,CAACH,WAAP,GAAqBA,WAArB;AACH;AACJ,WAPD,CAOE,OAAOjE,KAAP,EAAc;AACZC,YAAAA,OAAO,CAACqB,IAAR,CAAc,6CAA4C2B,QAAS,GAAnE,EAAuEjD,KAAvE;AACH;AACJ;AAED;AACJ;AACA;;;AACuC,eAArBsE,qBAAqB,GAAS;AACxCzH,UAAAA,YAAY,CAACqG,gBAAb,CAA8BrE,KAA9B;AACAhC,UAAAA,YAAY,CAACsG,eAAb,CAA6BtE,KAA7B;AACH;;AApe+D,O,UAkBjDI,O,GAAoC,IAAIsF,GAAJ,E,UAYpClH,Q,GAAgC,I,UAMhC6F,gB,GAAoD,IAAIqB,GAAJ,E,UACpDpB,e,GAA4D,IAAIoB,GAAJ,E,0FAnC1E7H,Q;;;;;iBAC+B,I;;yFAE/BA,Q;;;;;iBACgC,K;;sFAEhCA,Q;;;;;iBAC4B,E;;0FAE5BA,Q;;;;;iBACgC,I", "sourcesContent": ["import { _decorator, Component, Graphics, Node, find, Color, SpriteFrame, ImageAsset, assetManager, Sprite } from 'cc';\nimport { EDITOR } from 'cc/env';\nimport { GizmoDrawer, autoRegisterGizmoDrawers } from './GizmoDrawer';\n\n// Import gizmo classes to ensure their decorators are executed\nimport './EmitterGizmo';\n\nconst { ccclass, property, executeInEditMode, menu } = _decorator;\n\n// Scene events interface for editor integration\ninterface ISceneEvents {\n    onNodeAdded?(node: Node, opts?: any): void;\n    onNodeRemoved?(node: Node, opts?: any): void;\n    onComponentAdded?(comp: Component, opts?: any): void;\n    onComponentRemoved?(comp: Component, opts?: any): void;\n}\n\n/**\n * Global gizmo manager that handles all gizmo drawing\n * Should be attached to a global node in the scene\n * Implements ISceneEvents for automatic refresh on scene changes\n */\n@ccclass('GizmoManager')\n@menu('Gizmos/GizmoManager')\n@executeInEditMode(true)\nexport class GizmoManager extends Component implements ISceneEvents {\n    \n    @property\n    public gizmosEnabled: boolean = true;\n    \n    @property\n    public drawInPlayMode: boolean = false;\n    \n    @property\n    public refreshRate: number = 60; // FPS for gizmo updates\n    \n    @property\n    public maxDrawDistance: number = 2000; // Maximum distance to draw gizmos\n    \n    // Graphics component for drawing\n    private graphics: Graphics | null = null;\n    \n    // Registered gizmo drawers\n    private static drawers: Map<string, GizmoDrawer> = new Map();\n    \n    // Update timer\n    private updateTimer: number = 0;\n    private updateInterval: number = 1 / 60; // Default 60 FPS\n\n    // Scene change detection\n    private lastSceneNodeCount: number = 0;\n    private sceneChangeCheckInterval: number = 0.1; // Check every 100ms\n    private sceneChangeTimer: number = 0;\n\n    // Singleton instance\n    private static instance: GizmoManager | null = null;\n    public static get Instance() {\n        return GizmoManager.instance;\n    }\n\n    // Sprite frame cache for icons\n    private static spriteFrameCache: Map<string, SpriteFrame | null> = new Map();\n    private static loadingPromises: Map<string, Promise<SpriteFrame | null>> = new Map();\n    \n    protected onLoad(): void {\n        if (!EDITOR && !this.drawInPlayMode) return;\n\n        // Set as singleton instance\n        GizmoManager.instance = this;\n\n        // Ensure the gizmo manager node is positioned at origin for proper coordinate mapping\n        this.node.setPosition(0, 0, 0);\n        this.node.setRotation(0, 0, 0, 1);\n        this.node.setScale(1, 1, 1);\n\n        // Get or create Graphics component\n        this.graphics = this.getComponent(Graphics) || this.addComponent(Graphics);\n\n        // Update refresh interval\n        this.updateInterval = 1 / this.refreshRate;\n\n        // Auto-register all decorated gizmo drawers\n        GizmoManager.autoRegisterDrawers();\n\n        // Initialize scene change detection\n        const rootNodes = this.findAllRootNodes();\n        this.lastSceneNodeCount = this.countAllNodesRecursive(rootNodes);\n\n        // Register for editor scene events if available\n        this.registerSceneEvents();\n        this.registerSelectionEvents();\n    }\n    \n    protected onDestroy(): void {\n        if (GizmoManager.instance === this) {\n            GizmoManager.instance = null;\n        }\n\n        // Unregister scene events\n        this.unregisterSceneEvents();\n    }\n    \n    protected update(deltaTime: number): void {\n        if (!EDITOR && !this.drawInPlayMode) return;\n        if (!this.graphics) return;\n\n        // Check for scene changes periodically\n        this.sceneChangeTimer += deltaTime;\n        let forceRedraw = false;\n        if (this.sceneChangeTimer >= this.sceneChangeCheckInterval) {\n            this.sceneChangeTimer = 0;\n            forceRedraw = this.checkForSceneChanges();\n        }\n\n        // Throttle updates based on refresh rate (unless forced by scene change)\n        this.updateTimer += deltaTime;\n        if (!forceRedraw && this.updateTimer < this.updateInterval) return;\n        this.updateTimer = 0;\n\n        if (this.gizmosEnabled) {\n            this.drawAllGizmos();\n        } else {\n            // Clear graphics when gizmos are disabled\n            this.graphics.clear();\n        }\n    }\n    \n    /**\n     * Draw all gizmos for all registered drawers\n     */\n    private drawAllGizmos(): void {\n        if (!this.graphics)\n            return;\n\n        // Clear previous drawings\n        this.graphics.clear();\n\n        // Get all drawers sorted by priority\n        const sortedDrawers = Array.from(GizmoManager.drawers.values())\n            .filter(drawer => drawer.enabled)\n            .sort((a, b) => a.getPriority() - b.getPriority());\n\n        // Find all nodes in the scene\n        const rootNodes = this.findAllRootNodes();\n\n        // Draw gizmos for each drawer\n        for (const drawer of sortedDrawers) {\n            this.drawGizmosForDrawer(drawer, rootNodes);\n        }\n    }\n    \n    /**\n     * Draw gizmos for a specific drawer\n     */\n    private drawGizmosForDrawer(drawer: GizmoDrawer, rootNodes: Node[]): void {\n        if (!this.graphics) return;\n\n        const componentsToProcess: { component: Component, node: Node }[] = [];\n\n        // Find all components of the drawer's type\n        for (const rootNode of rootNodes) {\n            this.findComponentsRecursive(rootNode, drawer, componentsToProcess);\n        }\n\n        // Draw gizmos for each component\n        for (const { component, node } of componentsToProcess) {\n            try {\n                drawer.drawGizmos(component, this.graphics, node);\n            } catch (error) {\n                console.error(`GizmoManager: Error drawing gizmos for ${drawer.drawerName}:`, error);\n            }\n        }\n    }\n    \n    /**\n     * Recursively find components that match the drawer's type\n     */\n    private findComponentsRecursive(node: Node, drawer: GizmoDrawer, results: { component: Component, node: Node }[]): void {\n        // Check distance from gizmo manager\n        const distance = node.worldPosition.subtract(this.node.worldPosition).length();\n        if (distance > this.maxDrawDistance) return;\n        \n        // Check components on this node\n        const components = node.getComponents(Component);\n        for (const component of components) {\n            if (drawer.canHandle(component)) {\n                results.push({ component, node });\n            }\n        }\n        \n        // Recursively check children\n        for (const child of node.children) {\n            this.findComponentsRecursive(child, drawer, results);\n        }\n    }\n    \n    /**\n     * Check if the scene has changed (nodes added/removed)\n     * Returns true if a redraw should be forced\n     */\n    private checkForSceneChanges(): boolean {\n        const rootNodes = this.findAllRootNodes();\n        const currentNodeCount = this.countAllNodesRecursive(rootNodes);\n\n        if (currentNodeCount !== this.lastSceneNodeCount) {\n            this.lastSceneNodeCount = currentNodeCount;\n            return true; // Force redraw\n        }\n\n        return false;\n    }\n\n    /**\n     * Count all nodes recursively in the scene\n     */\n    private countAllNodesRecursive(nodes: Node[]): number {\n        let count = nodes.length;\n        for (const node of nodes) {\n            count += this.countAllNodesRecursive(node.children);\n        }\n        return count;\n    }\n\n    /**\n     * Find all root nodes in the scene\n     */\n    private findAllRootNodes(): Node[] {\n        const scene = this.node.scene;\n        if (!scene) return [];\n\n        return scene.children.filter(child => child !== this.node);\n    }\n    \n    /**\n     * Register a gizmo drawer\n     */\n    public static registerDrawer(drawer: GizmoDrawer): void {\n        const key = drawer.drawerName;\n        \n        if (GizmoManager.drawers.has(key)) {\n            console.warn(`GizmoManager: Drawer ${key} is already registered`);\n            return;\n        }\n        \n        GizmoManager.drawers.set(key, drawer);\n        drawer.onRegister();\n        \n        if (EDITOR) {\n            console.log(`GizmoManager: Registered drawer ${key}`);\n        }\n    }\n    \n    /**\n     * Unregister a gizmo drawer\n     */\n    public static unregisterDrawer(drawerName: string): boolean {\n        const drawer = GizmoManager.drawers.get(drawerName);\n        if (!drawer) return false;\n        \n        drawer.onUnregister();\n        GizmoManager.drawers.delete(drawerName);\n        \n        if (EDITOR) {\n            console.log(`GizmoManager: Unregistered drawer ${drawerName}`);\n        }\n        \n        return true;\n    }\n    \n    /**\n     * Get a registered drawer by name\n     */\n    public static getDrawer(drawerName: string): GizmoDrawer | null {\n        return GizmoManager.drawers.get(drawerName) || null;\n    }\n    \n    /**\n     * Get all registered drawers\n     */\n    public static getAllDrawers(): GizmoDrawer[] {\n        return Array.from(GizmoManager.drawers.values());\n    }\n    \n    /**\n     * Get the singleton instance\n     */\n    public static getInstance(): GizmoManager | null {\n        return GizmoManager.instance;\n    }\n    \n    /**\n     * Auto-register all decorated gizmo drawers\n     */\n    public static autoRegisterDrawers(): void {\n        autoRegisterGizmoDrawers((drawer: GizmoDrawer) => {\n            GizmoManager.registerDrawer(drawer);\n        });\n    }\n\n    /**\n     * Force refresh all gizmos and reset scene change detection\n     */\n    public static refresh(): void {\n        const instance = GizmoManager.getInstance();\n        if (instance) {\n            // Reset scene change detection to force immediate update\n            instance.lastSceneNodeCount = -1;\n            instance.drawAllGizmos();\n        }\n    }\n\n    /**\n     * Force immediate gizmo refresh (useful after scene modifications)\n     */\n    public static forceRefresh(): void {\n        const instance = GizmoManager.getInstance();\n        if (instance) {\n            // Update node count and force redraw\n            const rootNodes = instance.findAllRootNodes();\n            instance.lastSceneNodeCount = instance.countAllNodesRecursive(rootNodes);\n            instance.drawAllGizmos();\n        }\n    }\n\n    // ========== Scene Events Implementation ==========\n\n    /**\n     * Register for editor scene events\n     */\n    private registerSceneEvents(): void {\n        if (!EDITOR) return;\n\n        try {\n            // Try to register with the editor's scene event system\n            // This is a best-effort approach as the API may vary\n            const editorExtends = (globalThis as any).EditorExtends;\n            if (editorExtends && editorExtends.on) {\n                editorExtends.on('scene:node-added', this.onNodeAdded.bind(this));\n                editorExtends.on('scene:node-removed', this.onNodeRemoved.bind(this));\n                editorExtends.on('scene:component-added', this.onComponentAdded.bind(this));\n                editorExtends.on('scene:component-removed', this.onComponentRemoved.bind(this));\n            }\n\n        } catch (error) {\n            // Silent fallback - scene events not available\n        }\n    }\n\n    /**\n     * Unregister scene events\n     */\n    private unregisterSceneEvents(): void {\n        if (!EDITOR) return;\n\n        try {\n            const editorExtends = (globalThis as any).EditorExtends;\n            if (editorExtends && editorExtends.removeListener) {\n                editorExtends.removeListener('scene:node-added', this.onNodeAdded.bind(this));\n                editorExtends.removeListener('scene:node-removed', this.onNodeRemoved.bind(this));\n                editorExtends.removeListener('scene:component-added', this.onComponentAdded.bind(this));\n                editorExtends.removeListener('scene:component-removed', this.onComponentRemoved.bind(this));\n            }\n        } catch (error) {\n            // Silent fallback\n        }\n    }\n\n    private registerSelectionEvents(): void {\n        if (!EDITOR) return;\n        \n        // 这个暂时没法注册成功, 估计需要\n        // try {\n        //     // @ts-ignore\n        //     Editor.Ipc.addListener('_selection:selected', (event, uuids) => {\n        //         console.log('Selection changed:', uuids);\n        //     });\n\n        // } catch (error) {\n        //     console.warn('GizmoManager: Failed to register selection events', error);\n        // }\n    }\n        \n\n    /**\n     * Called when a node is added to the scene\n     */\n    public onNodeAdded(_node: Node, _opts?: any): void {\n        // Force immediate refresh when nodes are added\n        this.lastSceneNodeCount = -1; // Force count update\n        GizmoManager.forceRefresh();\n    }\n\n    /**\n     * Called when a node is removed from the scene\n     */\n    public onNodeRemoved(_node: Node, _opts?: any): void {\n        // Force immediate refresh when nodes are removed\n        this.lastSceneNodeCount = -1; // Force count update\n        GizmoManager.forceRefresh();\n    }\n\n    /**\n     * Called when a component is added to a node\n     */\n    public onComponentAdded(comp: Component, _opts?: any): void {\n        // Check if it's a component type we care about\n        for (const drawer of GizmoManager.drawers.values()) {\n            if (drawer.canHandle(comp)) {\n                // This is a component we draw gizmos for, force refresh\n                GizmoManager.forceRefresh();\n                break;\n            }\n        }\n    }\n\n    /**\n     * Called when a component is removed from a node\n     */\n    public onComponentRemoved(comp: Component, _opts?: any): void {\n        // Check if it's a component type we care about\n        for (const drawer of GizmoManager.drawers.values()) {\n            if (drawer.canHandle(comp)) {\n                // This is a component we draw gizmos for, force refresh\n                GizmoManager.forceRefresh();\n                break;\n            }\n        }\n    }\n\n    /**\n     * Load sprite frame with caching\n     */\n    public static async loadSpriteFrame(iconPath: string): Promise<SpriteFrame | null> {\n        // Check cache first\n        if (GizmoManager.spriteFrameCache.has(iconPath)) {\n            return GizmoManager.spriteFrameCache.get(iconPath) || null;\n        }\n\n        // Check if already loading\n        if (GizmoManager.loadingPromises.has(iconPath)) {\n            return GizmoManager.loadingPromises.get(iconPath) || null;\n        }\n\n        // Start loading\n        const loadingPromise = GizmoManager.loadSpriteFrameInternal(iconPath);\n        GizmoManager.loadingPromises.set(iconPath, loadingPromise);\n\n        const result = await loadingPromise;\n\n        // Cache the result and remove from loading promises\n        GizmoManager.spriteFrameCache.set(iconPath, result);\n        GizmoManager.loadingPromises.delete(iconPath);\n\n        return result;\n    }\n\n    /**\n     * Internal method to load sprite frame\n     */\n    private static async loadSpriteFrameInternal(iconPath: string): Promise<SpriteFrame | null> {\n        try {\n            const fullPath = `db://assets/editor/icons/${iconPath}`;\n            // @ts-ignore\n            const uuid = await Editor.Message.request('asset-db', 'query-uuid', fullPath);\n            if (!uuid) {\n                console.warn(`Failed to query uuid for icon: ${fullPath}`);\n                return null;\n            }\n\n            return new Promise<SpriteFrame | null>((resolve) => {\n                assetManager.loadAny<ImageAsset>(uuid, (err:Error|null, asset: ImageAsset) => {\n                    if (err) {\n                        console.warn(`Failed to load icon: ${fullPath}`, err);\n                        resolve(null);\n                        return;\n                    }\n\n                    const spriteFrame = SpriteFrame.createWithImage(asset);\n                    resolve(spriteFrame);\n                });\n            });\n        } catch (error) {\n            console.warn(`Failed to load icon: ${iconPath}`, error);\n            return null;\n        }\n    }\n\n    /**\n     * Load and apply sprite frame to a sprite component\n     */\n    public static async loadAndApplySpriteFrame(iconPath: string, sprite: Sprite): Promise<void> {\n        try {\n            const spriteFrame = await GizmoManager.loadSpriteFrame(iconPath);\n\n            // Apply to sprite if still valid\n            if (sprite && sprite.isValid && spriteFrame) {\n                sprite.spriteFrame = spriteFrame;\n            }\n        } catch (error) {\n            console.warn(`Failed to load and apply sprite frame for ${iconPath}:`, error);\n        }\n    }\n\n    /**\n     * Clear sprite frame cache\n     */\n    public static clearSpriteFrameCache(): void {\n        GizmoManager.spriteFrameCache.clear();\n        GizmoManager.loadingPromises.clear();\n    }\n}\n"]}