{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelWaveUI.ts"], "names": ["_decorator", "CCInteger", "Vec2", "Prefab", "ccclass", "property", "LevelWaveGroup"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAA0BC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;;;;;;;;;OAE9C;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;gCAGjBM,c,WADZF,OAAO,CAAC,gBAAD,C,UAEHC,QAAQ,CAACF,MAAD,C,UAERE,QAAQ,CAACH,IAAD,C,UAERG,QAAQ,CAACJ,SAAD,C,2BANb,MACaK,cADb,CAC4B;AAAA;AAAA;;AAAA;;AAAA;AAAA;;AAAA,O;;;;;iBAES,I;;;;;;;iBAEP,IAAIJ,IAAJ,E;;;;;;;iBAEF,E", "sourcesContent": ["import { _decorator, assetManager, CCInteger, Vec2, Prefab } from 'cc';\r\nimport { LevelElemUI } from './LevelElemUI';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('LevelWaveGroup')\r\nexport class LevelWaveGroup {\r\n    @property(Prefab)\r\n    public wavePrefab: Prefab|null = null;\r\n    @property(Vec2)\r\n    public waveOffset: Vec2 = new Vec2();\r\n    @property(CCInteger)\r\n    public weight: number = 50;\r\n}\r\n"]}