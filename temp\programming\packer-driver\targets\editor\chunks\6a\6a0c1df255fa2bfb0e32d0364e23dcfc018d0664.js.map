{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/LevelEditorEventUI.ts"], "names": ["_decorator", "Enum", "Vec3", "CCString", "assetManager", "UITransform", "Sprite", "SpriteFrame", "instantiate", "Vec2", "AudioClip", "LevelDataEventTriggerType", "LevelDataEventTriggerLog", "LevelDataEventWaveGroup", "eLevelSpecialEvent", "newTrigger", "LevelDataEventCondtionType", "Wave", "WavePreview", "LevelEditorElemUI", "LevelEditorWaveGroup", "LevelEditorCondition", "GameConst", "ccclass", "property", "executeInEditMode", "LevelEditorEventTrigger", "type", "visible", "Log", "Audio", "displayName", "SpecialEvent", "_index", "data", "_audio", "_waveGroup", "_type", "value", "message", "audio", "audioUUID", "uuid", "waveGroup", "for<PERSON>ach", "levelDataWaveGroup", "prefab", "wave", "waveUUID", "waveOffset", "weight", "push", "setWaveOffset", "offset", "eventType", "copyFrom", "source", "map", "newWaveGroup", "wavePrefab", "LevelEditorEventUI", "sprite", "_isDirty", "_waveMap", "Map", "_wavePreviews", "_isPlaying", "_isActive", "_positionBeforePlay", "onLoad", "node", "getComponent", "addComponent", "eventSpriteUUID", "loadAny", "err", "asset", "console", "warn", "spriteFrame", "createWithImage", "uiTransform", "setContentSize", "onFocusInEditor", "update", "dt", "i", "conditions", "length", "cond", "targetElemID", "_targetElem", "elems", "scene", "getComponentsInChildren", "elem", "elemID", "syncWaveOffset", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clear", "triggers", "trigger", "waveNode", "<PERSON><PERSON><PERSON><PERSON>", "setPosition", "x", "y", "setupWave", "set", "size", "key", "position", "initByLevelData", "condition", "error", "waveTrigger", "editorWaveGroup", "fillLevelData", "remark", "newCond", "setupInEditor", "play", "bPlay", "progress", "getPosition", "posY", "VIEWPORT_TOP", "performTrigger", "z", "resetPlay", "wavePreview", "clearPreview", "offsetY", "log", "triggerWave", "randomWeight", "Math", "floor", "random", "curWeight", "selectedWaveGroup", "get", "getComponentInChildren", "triggerPreview"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAA2DC,MAAAA,I,OAAAA,I;AAA4BC,MAAAA,Q,OAAAA,Q;AAAiCC,MAAAA,Y,OAAAA,Y;AAAcC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;AAAgCC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;AAAiBC,MAAAA,S,OAAAA,S;;AAI9MC,MAAAA,yB,iBAAAA,yB;;AACvBC,MAAAA,wB,iBAAAA,wB;;AAE+CC,MAAAA,uB,iBAAAA,uB;;AACZC,MAAAA,kB,iBAAAA,kB;;AACnCC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,0B,iBAAAA,0B;;AAEAC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,W,iBAAAA,W;;AAGAC,MAAAA,iB,kBAAAA,iB;;AACAC,MAAAA,oB,kBAAAA,oB;;AACAC,MAAAA,oB,kBAAAA,oB;;AACAC,MAAAA,S,kBAAAA,S;;;;;;;;;OAlBH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CzB,U;;yCAqBpC0B,uB,WADZH,OAAO,CAAC,yBAAD,C,UAKHC,QAAQ,CAAC;AACNG,QAAAA,IAAI,EAAC1B,IAAI;AAAA;AAAA;AADH,OAAD,C,UAYRuB,QAAQ,CAAC;AACNG,QAAAA,IAAI,EAAExB,QADA;;AAENyB,QAAAA,OAAO,GAAI;AACP,iBAAO,KAAKD,IAAL,IAAa;AAAA;AAAA,sEAA0BE,GAA9C;AACH;;AAJK,OAAD,C,UAcRL,QAAQ,CAAC;AACNG,QAAAA,IAAI,EAAEjB,SADA;;AAENkB,QAAAA,OAAO,GAAI;AACP,iBAAO,KAAKD,IAAL,IAAa;AAAA;AAAA,sEAA0BG,KAA9C;AACH;;AAJK,OAAD,C,UAmBRN,QAAQ,CAAC;AACNG,QAAAA,IAAI,EAAE;AAAA;AAAA,yDADA;AAENI,QAAAA,WAAW,EAAE,OAFP;;AAGNH,QAAAA,OAAO,GAAI;AACP,iBAAO,KAAKD,IAAL,IAAa;AAAA;AAAA,sEAA0BV,IAA9C;AACH;;AALK,OAAD,C,UAgDRO,QAAQ,CAAC;AACNG,QAAAA,IAAI,EAAE1B,IAAI;AAAA;AAAA,qDADJ;;AAEN2B,QAAAA,OAAO,GAAI;AACP,iBAAO,KAAKD,IAAL,IAAa;AAAA;AAAA,sEAA0BK,YAA9C;AACH;;AAJK,OAAD,C,2BAlGb,MACaN,uBADb,CACoC;AAAA;AAAA,eACzBO,MADyB,GAChB,CADgB;AAAA,eAEzBC,IAFyB,GAEM;AAAA;AAAA,qEAFN;AAAA,eA6BzBC,MA7ByB,GA6BA,IA7BA;AAAA,eAgDzBC,UAhDyB,GAgDY,EAhDZ;AAAA;;AAOjB,YAAJT,IAAI,GAA6B;AACxC,iBAAO,KAAKO,IAAL,CAAUG,KAAjB;AACH;;AACc,YAAJV,IAAI,CAACW,KAAD,EAAmC;AAC9C,cAAI,KAAKJ,IAAL,CAAUG,KAAV,IAAmBC,KAAvB,EAA8B;AAC1B,iBAAKJ,IAAL,GAAY;AAAA;AAAA,0CAAW;AAACG,cAAAA,KAAK,EAAEC;AAAR,aAAX,CAAZ;AACH;AACJ;;AAQiB,YAAPC,OAAO,GAAW;AACzB,iBAAQ,KAAKL,IAAN,CAAwCK,OAA/C;AACH;;AACiB,YAAPA,OAAO,CAACD,KAAD,EAAgB;AAC7B,eAAKJ,IAAN,CAAwCK,OAAxC,GAAkDD,KAAlD;AACH;;AASe,YAALE,KAAK,GAAmB;AAC/B,iBAAO,KAAKL,MAAZ;AACH;;AACe,YAALK,KAAK,CAACF,KAAD,EAAwB;AACpC,eAAKH,MAAL,GAAcG,KAAd;;AACA,cAAIA,KAAJ,EAAW;AACN,iBAAKJ,IAAN,CAA0CO,SAA1C,GAAsDH,KAAK,CAACI,IAA5D;AACH,WAFD,MAEO;AACF,iBAAKR,IAAN,CAA0CO,SAA1C,GAAsD,EAAtD;AACH;AACJ;;AAUmB,YAATE,SAAS,GAA2B;AAC3C,iBAAO,KAAKP,UAAZ;AACH;;AACmB,YAATO,SAAS,CAACL,KAAD,EAAgC;AAChD,eAAKF,UAAL,GAAkBE,KAAlB;;AACA,cAAIA,KAAJ,EAAW;AACN,iBAAKJ,IAAN,CAAyCS,SAAzC,GAAqD,EAArD;AACAL,YAAAA,KAAK,CAACM,OAAN,CAAeD,SAAD,IAAe;AACzB,kBAAIE,kBAAkB,GAAG;AAAA;AAAA,uEAAzB;;AACA,kBAAIF,SAAS,CAACG,MAAd,EAAsB;AAClBD,gBAAAA,kBAAkB,CAACE,IAAnB,CAAwBC,QAAxB,GAAmCL,SAAS,CAACG,MAAV,CAAiBJ,IAApD;AACH;;AACDG,cAAAA,kBAAkB,CAACE,IAAnB,CAAwBE,UAAxB,GAAqCN,SAAS,CAACI,IAAV,CAAeE,UAApD;AACAJ,cAAAA,kBAAkB,CAACK,MAAnB,GAA4BP,SAAS,CAACO,MAAtC;AACC,mBAAKhB,IAAN,CAAyCS,SAAzC,CAAmDQ,IAAnD,CAAwDN,kBAAxD;AACH,aARD;AASH,WAXD,MAWO;AACF,iBAAKX,IAAN,CAAyCS,SAAzC,GAAqD,EAArD;AACH;AACJ;;AAEMS,QAAAA,aAAa,CAACJ,QAAD,EAAmBK,MAAnB,EAAiC;AACjD,eAAK,IAAIV,SAAT,IAAsB,KAAKA,SAA3B,EAAsC;AAClC,gBAAIA,SAAS,CAACG,MAAV,IAAoBH,SAAS,CAACG,MAAV,CAAiBJ,IAAjB,IAAyBM,QAAjD,EAA2D;AACvDL,cAAAA,SAAS,CAACI,IAAV,CAAeE,UAAf,GAA4BI,MAA5B;AACA;AACH;AACJ;;AAEA,eAAKnB,IAAN,CAAyCS,SAAzC,GAAqD,EAArD;AACA,eAAKA,SAAL,CAAeC,OAAf,CAAwBD,SAAD,IAAe;AAClC,gBAAIE,kBAAkB,GAAG;AAAA;AAAA,qEAAzB;;AACA,gBAAIF,SAAS,CAACG,MAAd,EAAsB;AAClBD,cAAAA,kBAAkB,CAACE,IAAnB,CAAwBC,QAAxB,GAAmCL,SAAS,CAACG,MAAV,CAAiBJ,IAApD;AACH;;AACDG,YAAAA,kBAAkB,CAACE,IAAnB,CAAwBE,UAAxB,GAAqCN,SAAS,CAACI,IAAV,CAAeE,UAApD;AACAJ,YAAAA,kBAAkB,CAACK,MAAnB,GAA4BP,SAAS,CAACO,MAAtC;AACC,iBAAKhB,IAAN,CAAyCS,SAAzC,CAAmDQ,IAAnD,CAAwDN,kBAAxD;AACH,WARD;AASH;;AAQmB,YAATS,SAAS,GAAuB;AACvC,iBAAQ,KAAKpB,IAAN,CAAiDoB,SAAxD;AACH;;AACmB,YAATA,SAAS,CAAChB,KAAD,EAA4B;AAC3C,eAAKJ,IAAN,CAAiDoB,SAAjD,GAA6DhB,KAA7D;AACH;;AAEMiB,QAAAA,QAAQ,CAACC,MAAD,EAAkC;AAC7C,eAAKvB,MAAL,GAAcuB,MAAM,CAACvB,MAArB;AACA,eAAKN,IAAL,GAAY6B,MAAM,CAAC7B,IAAnB;;AACA,kBAAQ,KAAKA,IAAb;AACI,iBAAK;AAAA;AAAA,wEAA0BE,GAA/B;AACI,mBAAKU,OAAL,GAAeiB,MAAM,CAACjB,OAAtB;AACA;;AACJ,iBAAK;AAAA;AAAA,wEAA0BT,KAA/B;AACI,mBAAKU,KAAL,GAAagB,MAAM,CAAChB,KAApB;AACA;;AACJ,iBAAK;AAAA;AAAA,wEAA0BvB,IAA/B;AACI,mBAAK0B,SAAL,GAAiBa,MAAM,CAACb,SAAP,CAAiBc,GAAjB,CAAsBd,SAAD,IAAe;AACjD,oBAAIe,YAAY,GAAG;AAAA;AAAA,mEAAnB;AACAA,gBAAAA,YAAY,CAACX,IAAb,CAAkBY,UAAlB,GAA+BhB,SAAS,CAACI,IAAV,CAAeY,UAA9C;AACAD,gBAAAA,YAAY,CAACX,IAAb,CAAkBE,UAAlB,GAA+BN,SAAS,CAACI,IAAV,CAAeE,UAA9C;AACAS,gBAAAA,YAAY,CAACR,MAAb,GAAsBP,SAAS,CAACO,MAAhC;AACA,uBAAOQ,YAAP;AACH,eANgB,CAAjB;AAOA;;AACJ,iBAAK;AAAA;AAAA,wEAA0B1B,YAA/B;AACI,mBAAKsB,SAAL,GAAiBE,MAAM,CAACF,SAAxB;AACA;;AACJ;AACI;AApBR;AAsBH;;AAvI+B,O;;oCA4IvBM,kB,YAFZrC,OAAO,CAAC,oBAAD,C,UACPE,iBAAiB,E,UAEbD,QAAQ,CAAC;AAAA;AAAA,uDAAD,C,WAERA,QAAQ,CAAC,CAACE,uBAAD,CAAD,C,6CALb,MAEakC,kBAFb;AAAA;AAAA,kDAE0D;AAAA;AAAA;;AAAA;;AAAA;;AAAA,eAM9CC,MAN8C,GAMtB,IANsB;AAsBtD;AAtBsD,eAuB9CC,QAvB8C,GAuBnC,KAvBmC;AAAA,eA4B9CC,QA5B8C,GA4BhB,IAAIC,GAAJ,EA5BgB;AAAA,eA6B9CC,aA7B8C,GA6Bf,EA7Be;AAAA,eA+K9CC,UA/K8C,GA+KjC,KA/KiC;AAAA,eAgL9CC,SAhL8C,GAgLlC,KAhLkC;AAAA,eAiL9CC,mBAjL8C,GAiLlB,IAAIlE,IAAJ,EAjLkB;AAAA;;AAOtDmE,QAAAA,MAAM,GAAG;AACL,eAAKR,MAAL,GAAc,KAAKS,IAAL,CAAUC,YAAV,CAAuBjE,MAAvB,KAAkC,KAAKgE,IAAL,CAAUE,YAAV,CAAuBlE,MAAvB,CAAhD;AACA,gBAAMmE,eAAe,GAAG,sCAAxB;AACArE,UAAAA,YAAY,CAACsE,OAAb,CAAiCD,eAAjC,EAAkD,CAACE,GAAD,EAAiBC,KAAjB,KAAuC;AACrF,gBAAID,GAAJ,EAAS;AACLE,cAAAA,OAAO,CAACC,IAAR,CAAaH,GAAb;AACA;AACH;;AAED,iBAAKd,MAAL,CAAakB,WAAb,GAA2BxE,WAAW,CAACyE,eAAZ,CAA4BJ,KAA5B,CAA3B;AACA,kBAAMK,WAAW,GAAG,KAAKX,IAAL,CAAUC,YAAV,CAAuBlE,WAAvB,KAAuC,KAAKiE,IAAL,CAAUE,YAAV,CAAuBnE,WAAvB,CAA3D;AACA4E,YAAAA,WAAW,CAACC,cAAZ,CAA2B,GAA3B,EAAgC,GAAhC;AACH,WATD;AAUH;;AAIDC,QAAAA,eAAe,GAAS;AACpB,eAAKrB,QAAL,GAAgB,IAAhB;AACH;;AAIMsB,QAAAA,MAAM,CAACC,EAAD,EAAmB;AAC5B,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,UAAL,CAAgBC,MAApC,EAA4CF,CAAC,EAA7C,EAAiD;AAC7C,kBAAMG,IAAI,GAAG,KAAKF,UAAL,CAAgBD,CAAhB,CAAb;AACAG,YAAAA,IAAI,CAACxD,MAAL,GAAcqD,CAAd;;AACA,gBAAIG,IAAI,CAAC9D,IAAL,IAAa;AAAA;AAAA,0EAA2BV,IAAxC,IACIwE,IAAI,CAACvD,IAAN,CAA0CwD,YAA1C,IAA0D,EAD7D,IAEGD,IAAI,CAACE,WAAL,IAAoB,IAF3B,EAEiC;AAC7B,oBAAMC,KAAK,GAAG,KAAKtB,IAAL,CAAUuB,KAAV,CAAgBC,uBAAhB;AAAA;AAAA,yDAAd;;AACA,mBAAK,IAAIC,IAAT,IAAiBH,KAAjB,EAAwB;AACpB,oBAAIG,IAAI,CAACC,MAAL,IAAgBP,IAAI,CAACvD,IAAN,CAA0CwD,YAA7D,EAA2E;AACvED,kBAAAA,IAAI,CAACE,WAAL,GAAmBI,IAAnB;AACA;AACH;AACJ;AACJ;AACJ;;AAED,cAAI,KAAKjC,QAAT,EAAmB;AACf,iBAAKmC,cAAL,GADe,CAEf;;AACA,iBAAK3B,IAAL,CAAU4B,iBAAV;;AACA,iBAAKnC,QAAL,CAAcoC,KAAd,GAJe,CAMf;;;AACA,iBAAKC,QAAL,CAAcxD,OAAd,CAAuByD,OAAD,IAAa;AAC/BA,cAAAA,OAAO,CAAC1D,SAAR,CAAkBC,OAAlB,CAA2BD,SAAD,IAAe;AACrC,oBAAIA,SAAS,CAACI,IAAV,CAAeY,UAAnB,EAA+B;AAC3B,wBAAMb,MAAM,GAAGH,SAAS,CAACI,IAAV,CAAeY,UAA9B;AACA,wBAAM2C,QAAQ,GAAG9F,WAAW,CAACsC,MAAD,CAA5B;AACA,uBAAKwB,IAAL,CAAUiC,QAAV,CAAmBD,QAAnB;AACAA,kBAAAA,QAAQ,CAACE,WAAT,CAAqB7D,SAAS,CAACI,IAAV,CAAeE,UAAf,CAA0BwD,CAA/C,EAAkD9D,SAAS,CAACI,IAAV,CAAeE,UAAf,CAA0ByD,CAA5E;AACA,uBAAKC,SAAL,CAAeL,QAAQ,CAAC/B,YAAT;AAAA;AAAA,mCAAf;;AACA,uBAAKR,QAAL,CAAc6C,GAAd,CAAkB9D,MAAM,CAACJ,IAAzB,EAA+B4D,QAAQ,CAAC/B,YAAT;AAAA;AAAA,mCAA/B;AACH;AACJ,eATD;AAUH,aAXD;AAYA,iBAAKT,QAAL,GAAgB,KAAhB;AACH;AACJ;;AAEOmC,QAAAA,cAAc,GAAG;AACrB,cAAI,KAAKlC,QAAL,CAAc8C,IAAd,GAAqB,CAAzB,EAA4B;AACxB;AACA,iBAAK9C,QAAL,CAAcnB,OAAd,CAAsB,CAACG,IAAD,EAAO+D,GAAP,KAAe;AACjC,kBAAI,CAAC/D,IAAD,IAAS,CAACA,IAAI,CAACuB,IAAnB,EAAyB;AAEzB,oBAAMrB,UAAU,GAAGF,IAAI,CAACuB,IAAL,CAAUyC,QAA7B;;AACA,mBAAK,IAAIV,OAAT,IAAoB,KAAKD,QAAzB,EAAmC;AAC/BC,gBAAAA,OAAO,CAACjD,aAAR,CAAsB0D,GAAtB,EAA2B,IAAIrG,IAAJ,CAASwC,UAAU,CAACwD,CAApB,EAAuBxD,UAAU,CAACyD,CAAlC,CAA3B;AACH;AACJ,aAPD;AAQH;AACJ;;AAEMM,QAAAA,eAAe,CAAC9E,IAAD,EAAuB;AACzC,gBAAM8E,eAAN,CAAsB9E,IAAtB;;AACA,cAAIA,IAAI,CAACqD,UAAT,EAAqB;AACjB,iBAAK,IAAID,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGpD,IAAI,CAACqD,UAAL,CAAgBC,MAApC,EAA4CF,CAAC,EAA7C,EAAiD;AAC7C,oBAAM2B,SAAS,GAAG;AAAA;AAAA,iEAAlB;AACAA,cAAAA,SAAS,CAAChF,MAAV,GAAmBqD,CAAnB;AACA2B,cAAAA,SAAS,CAAC/E,IAAV,GAAiBA,IAAI,CAACqD,UAAL,CAAgBD,CAAhB,CAAjB;AACA,mBAAKC,UAAL,CAAgBpC,IAAhB,CAAqB8D,SAArB;AACH;AACJ;;AACD,cAAI/E,IAAI,CAACkE,QAAT,EAAmB;AACf,iBAAK,IAAId,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGpD,IAAI,CAACkE,QAAL,CAAcZ,MAAlC,EAA0CF,CAAC,EAA3C,EAA+C;AAC3C,oBAAMe,OAAO,GAAG,IAAI3E,uBAAJ,EAAhB;AACA2E,cAAAA,OAAO,CAACpE,MAAR,GAAiBqD,CAAjB;AACAe,cAAAA,OAAO,CAACnE,IAAR,GAAeA,IAAI,CAACkE,QAAL,CAAcd,CAAd,CAAf;;AACA,kBAAIe,OAAO,CAACnE,IAAR,CAAaG,KAAb,IAAsB;AAAA;AAAA,0EAA0BP,KAApD,EAA2D;AACvD,oBAAIY,IAAI,GAAI2D,OAAO,CAACnE,IAAT,CAA6CO,SAAxD;;AACA,oBAAIC,IAAI,IAAI,EAAZ,EAAgB;AACZtC,kBAAAA,YAAY,CAACsE,OAAb,CAAqB;AAAChC,oBAAAA,IAAI,EAACA;AAAN,mBAArB,EAAkC,CAACiC,GAAD,EAAMnC,KAAN,KAA0B;AACxD,wBAAImC,GAAJ,EAAS;AACLE,sBAAAA,OAAO,CAACqC,KAAR,CAAc,mDAAd,EAAmEvC,GAAnE;AACA;AACH;;AACD0B,oBAAAA,OAAO,CAAClE,MAAR,GAAiBK,KAAjB;AACH,mBAND;AAOH;AACJ;;AACD,kBAAI6D,OAAO,CAACnE,IAAR,CAAaG,KAAb,IAAsB;AAAA;AAAA,0EAA0BpB,IAApD,EAA0D;AACtD,oBAAIkG,WAAW,GAAGd,OAAO,CAACnE,IAA1B;;AACA,qBAAK,IAAIS,SAAT,IAAsBwE,WAAW,CAACxE,SAAlC,EAA6C;AACzC,sBAAIyE,eAAe,GAAG;AAAA;AAAA,qEAAtB,CADyC,CAEzC;;AACAhH,kBAAAA,YAAY,CAACsE,OAAb,CAAqB;AAAChC,oBAAAA,IAAI,EAACC,SAAS,CAACI,IAAV,CAAeC;AAArB,mBAArB,EAAqD,CAAC2B,GAAD,EAAM7B,MAAN,KAAwB;AACzE,wBAAI6B,GAAJ,EAAS;AACLE,sBAAAA,OAAO,CAACqC,KAAR,CAAc,yDAAd,EAAyEvC,GAAzE;AACA;AACH;;AACDyC,oBAAAA,eAAe,CAACrE,IAAhB,CAAqBY,UAArB,GAAkCb,MAAlC;AACH,mBAND;AAQAsE,kBAAAA,eAAe,CAACrE,IAAhB,CAAqBE,UAArB,GAAkCN,SAAS,CAACI,IAAV,CAAeE,UAAjD;AACAmE,kBAAAA,eAAe,CAAClE,MAAhB,GAAyBP,SAAS,CAACO,MAAnC;AACAmD,kBAAAA,OAAO,CAAC1D,SAAR,CAAkBQ,IAAlB,CAAuBiE,eAAvB;AACH;AACJ;;AACD,mBAAKhB,QAAL,CAAcjD,IAAd,CAAmBkD,OAAnB;AACH;AACJ;;AAED,eAAKvC,QAAL,GAAgB,IAAhB;AACH;;AAEMuD,QAAAA,aAAa,CAACnF,IAAD,EAAuB;AACvC,gBAAMmF,aAAN,CAAoBnF,IAApB;AACAA,UAAAA,IAAI,CAACqD,UAAL,GAAkB,EAAlB;AACA,eAAKA,UAAL,CAAgB3C,OAAhB,CAAyB6C,IAAD,IAAU;AAC9B,gBAAIA,IAAI,IAAI,IAAZ,EAAkB;AACdvD,cAAAA,IAAI,CAACqD,UAAL,CAAgBpC,IAAhB,CAAqBsC,IAAI,CAACvD,IAA1B;AACH;AACJ,WAJD;AAKA,eAAK+D,cAAL;AACA,eAAKG,QAAL,CAAcxD,OAAd,CAAuByD,OAAD,IAAa;AAC/B,gBAAIA,OAAO,IAAI,IAAf,EAAqB;AACjBnE,cAAAA,IAAI,CAACkE,QAAL,CAAcjD,IAAd,CAAmBkD,OAAO,CAACnE,IAA3B;AACH;AACJ,WAJD;AAKH;;AAEMqB,QAAAA,QAAQ,CAACC,MAAD,EAA6B;AACxC,eAAKwC,MAAL,GAAcxC,MAAM,CAACwC,MAArB;AACA,eAAKsB,MAAL,GAAc9D,MAAM,CAAC8D,MAArB;AACA,eAAKhD,IAAL,CAAUyC,QAAV,GAAqBvD,MAAM,CAACc,IAAP,CAAYyC,QAAjC;AACA,eAAKxB,UAAL,GAAkB/B,MAAM,CAAC+B,UAAP,CAAkB9B,GAAlB,CAAuBgC,IAAD,IAAU;AAC9C,gBAAI8B,OAAO,GAAG;AAAA;AAAA,+DAAd;AACAA,YAAAA,OAAO,CAAChE,QAAR,CAAiBkC,IAAjB;AACA,mBAAO8B,OAAP;AACH,WAJiB,CAAlB;AAIG;AACH,eAAKnB,QAAL,GAAgB5C,MAAM,CAAC4C,QAAP,CAAgB3C,GAAhB,CAAqB4C,OAAD,IAAa;AAC7C,gBAAItF,UAAU,GAAG,IAAIW,uBAAJ,EAAjB;AACAX,YAAAA,UAAU,CAACwC,QAAX,CAAoB8C,OAApB;AACA,mBAAOtF,UAAP;AACH,WAJe,CAAhB;AAIG;AACN;;AAEO4F,QAAAA,SAAS,CAAC5D,IAAD,EAAwB;AACrC,cAAIA,IAAI,IAAI,IAAZ,EACI;AAEJA,UAAAA,IAAI,CAACyE,aAAL;AACH;;AAKMC,QAAAA,IAAI,CAACC,KAAD,EAAiBC,QAAjB,EAAmC;AAC1C,cAAID,KAAK,IAAI,KAAKxD,UAAlB,EAA8B;AAC1B,iBAAKA,UAAL,GAAkBwD,KAAlB;;AACA,gBAAIA,KAAJ,EAAW;AACP,mBAAKpD,IAAL,CAAUsD,WAAV,CAAsB,KAAKxD,mBAA3B;AACH,aAFD,MAGK;AACD,mBAAKE,IAAL,CAAUkC,WAAV,CAAsB,KAAKpC,mBAA3B;AACH;AACJ;;AAED,cAAIsD,KAAJ,EAAW;AACP,kBAAMG,IAAI,GAAG,KAAKzD,mBAAL,CAAyBsC,CAAzB,GAA6BiB,QAA1C;;AACA,gBAAIE,IAAI,IAAI;AAAA;AAAA,wCAAUC,YAAtB,EAAoC;AAChC,kBAAI,CAAC,KAAK3D,SAAV,EAAqB;AACjB,qBAAKiC,QAAL,CAAcxD,OAAd,CAAuByD,OAAD,IAAa;AAC/B,uBAAK0B,cAAL,CAAoB1B,OAAO,CAACnE,IAA5B,EAAkC2F,IAAlC;AACH,iBAFD;AAGA,qBAAK1D,SAAL,GAAiB,IAAjB;AACH;AACJ;;AACD,iBAAKG,IAAL,CAAUkC,WAAV,CAAsB,KAAKpC,mBAAL,CAAyBqC,CAA/C,EAAkDoB,IAAlD,EAAwD,KAAKzD,mBAAL,CAAyB4D,CAAjF;AACH,WAXD,MAYK;AACD,gBAAI,KAAK7D,SAAT,EAAoB;AAChB,mBAAK8D,SAAL;AACA,mBAAK9D,SAAL,GAAiB,KAAjB;AACH;AACJ;AACJ;;AAEO8D,QAAAA,SAAS,GAAG;AAChB,eAAKhE,aAAL,CAAmBrB,OAAnB,CAA4BsF,WAAD,IAAiB;AACxCA,YAAAA,WAAW,CAACC,YAAZ;AACH,WAFD;;AAGA,eAAKlE,aAAL,GAAqB,EAArB;AACH;;AAEO8D,QAAAA,cAAc,CAAC1B,OAAD,EAAiC+B,OAAjC,EAAkD;AACpE,kBAAQ/B,OAAO,CAAChE,KAAhB;AACI,iBAAK;AAAA;AAAA,wEAA0BR,GAA/B;AACIgD,cAAAA,OAAO,CAACwD,GAAR,CAAY,oBAAZ,EAAkC,aAAlC,EAAkDhC,OAAD,CAAsC9D,OAAvF;AACA;;AACJ,iBAAK;AAAA;AAAA,wEAA0BT,KAA/B;AACI;;AACJ,iBAAK;AAAA;AAAA,wEAA0Bb,IAA/B;AACI;AACA,mBAAKqH,WAAL,CAAiBjC,OAAjB,EAAuD+B,OAAvD;AACA;;AACJ;AACI;AAXR;AAaH;;AAEOE,QAAAA,WAAW,CAACA,WAAD,EAAyCF,OAAzC,EAA0D;AACzEvD,UAAAA,OAAO,CAACwD,GAAR,CAAY,mBAAZ,EAAiCD,OAAjC;AACA,gBAAMG,YAAY,GAAGC,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgB,GAA3B,CAArB;AACA,cAAIC,SAAS,GAAG,CAAhB;AACA,cAAIC,iBAAiD,GAAG,IAAxD;;AACA,eAAK,IAAIjG,SAAT,IAAsB2F,WAAW,CAAC3F,SAAlC,EAA6C;AACzCgG,YAAAA,SAAS,IAAIhG,SAAS,CAACO,MAAvB;;AACA,gBAAIqF,YAAY,IAAII,SAApB,EAA+B;AAC3BC,cAAAA,iBAAiB,GAAGjG,SAApB;AACA;AACH;AACJ;;AACD,cAAIiG,iBAAiB,IAAI,IAAzB,EAA+B;AAC3B;AACH;;AACD,cAAI7F,IAAI,GAAG,KAAKgB,QAAL,CAAc8E,GAAd,CAAkBD,iBAAiB,CAAC7F,IAAlB,CAAuBC,QAAzC,CAAX;;AACA,cAAID,IAAJ,EAAU;AACN,kBAAMgE,QAAQ,GAAG,KAAKzC,IAAL,CAAUyC,QAA3B;AACA,kBAAMmB,WAAW,GAAGnF,IAAI,CAACuB,IAAL,CAAUwE,sBAAV;AAAA;AAAA,2CAApB;;AACA,gBAAIZ,WAAJ,EAAiB;AACbA,cAAAA,WAAW,CAACa,cAAZ,CAA2BhC,QAAQ,CAACN,CAApC,EAAuC2B,OAAvC;;AACA,mBAAKnE,aAAL,CAAmBd,IAAnB,CAAwB+E,WAAxB;AACH;AACJ;AACJ;;AAhQqD,O;;;;;iBAEV,E;;;;;;;iBAEC,E", "sourcesContent": ["import { _decorator, Enum, CCFloat, Component, JsonAsset, Node, Prefab, Slider, Vec3, ValueType, CCBoolean, CCString, ImageAsset, resources, assetManager, UITransform, Sprite, SpriteFrame, SpriteAtlas, math, instantiate, Vec2, CCInteger, AudioClip, BitMask } from 'cc';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\n\r\nimport { LevelDataEvent } from 'db://assets/bundles/common/script/leveldata/leveldata';\r\nimport { LevelDataEventTrigger, LevelDataEventTriggerType } from 'db://assets/bundles/common/script/leveldata/trigger/LevelDataEventTrigger';\r\nimport { LevelDataEventTriggerLog } from 'db://assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerLog';\r\nimport { LevelDataEventTriggerAudio } from 'db://assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerAudio';\r\nimport { LevelDataEventTriggerWave, LevelDataEventWave, LevelDataEventWaveGroup } from 'db://assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerWave';\r\nimport { LevelDataEventTriggerSpecialEvent, eLevelSpecialEvent } from 'db://assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerSpecialEvent';\r\nimport { newTrigger } from 'db://assets/bundles/common/script/leveldata/trigger/newTrigger';\r\nimport { LevelDataEventCondtionType } from 'db://assets/bundles/common/script/leveldata/condition/LevelDataEventCondtion';\r\nimport { LevelDataEventCondtionWave } from 'db://assets/bundles/common/script/leveldata/condition/LevelDataEventCondtionWave';\r\nimport { Wave } from 'db://assets/bundles/common/script/game/wave/Wave';\r\nimport { WavePreview } from './preview/WavePreview';\r\nimport { newCondition } from 'db://assets/bundles/common/script/leveldata/condition/newCondition';\r\n\r\nimport { LevelEditorElemUI } from './LevelEditorElemUI';\r\nimport { LevelEditorWaveGroup } from './LevelEditorWaveParam';\r\nimport { LevelEditorCondition } from './LevelEditorCondition';\r\nimport { GameConst } from '../../scripts/core/base/GameConst';\r\n\r\n@ccclass('LevelEditorEventTrigger')\r\nexport class LevelEditorEventTrigger{\r\n    public _index = 0;\r\n    public data : LevelDataEventTrigger = new LevelDataEventTriggerLog();\r\n\r\n    @property({\r\n        type:Enum(LevelDataEventTriggerType),\r\n    })\r\n    public get type(): LevelDataEventTriggerType{\r\n        return this.data._type;\r\n    }\r\n    public set type(value: LevelDataEventTriggerType) {\r\n        if (this.data._type != value) {\r\n            this.data = newTrigger({_type: value});\r\n        }\r\n    }\r\n\r\n    @property({\r\n        type :CCString,\r\n        visible () {\r\n            return this.type == LevelDataEventTriggerType.Log ;\r\n        }\r\n    })\r\n    public get message(): string {\r\n        return (this.data as LevelDataEventTriggerLog).message;\r\n    }\r\n    public set message(value: string) {\r\n        (this.data as LevelDataEventTriggerLog).message = value;\r\n    }\r\n\r\n    public _audio: AudioClip|null = null;\r\n    @property({\r\n        type :AudioClip,\r\n        visible () {\r\n            return this.type == LevelDataEventTriggerType.Audio;\r\n        }\r\n    })\r\n    public get audio(): AudioClip|null {\r\n        return this._audio;\r\n    }\r\n    public set audio(value: AudioClip|null) {\r\n        this._audio = value;\r\n        if (value) {\r\n            (this.data as LevelDataEventTriggerAudio).audioUUID = value.uuid;\r\n        } else {\r\n            (this.data as LevelDataEventTriggerAudio).audioUUID = \"\";\r\n        }\r\n    }\r\n\r\n    public _waveGroup: LevelEditorWaveGroup[] = []; \r\n    @property({\r\n        type: [LevelEditorWaveGroup],\r\n        displayName: \"波次组随机\",\r\n        visible () {\r\n            return this.type == LevelDataEventTriggerType.Wave;\r\n        }\r\n    })\r\n    public get waveGroup(): LevelEditorWaveGroup[] {\r\n        return this._waveGroup;\r\n    }\r\n    public set waveGroup(value: LevelEditorWaveGroup[]) {\r\n        this._waveGroup = value;\r\n        if (value) {\r\n            (this.data as LevelDataEventTriggerWave).waveGroup = [];\r\n            value.forEach((waveGroup) => {\r\n                let levelDataWaveGroup = new LevelDataEventWaveGroup();\r\n                if (waveGroup.prefab) {\r\n                    levelDataWaveGroup.wave.waveUUID = waveGroup.prefab.uuid;\r\n                }\r\n                levelDataWaveGroup.wave.waveOffset = waveGroup.wave.waveOffset;\r\n                levelDataWaveGroup.weight = waveGroup.weight;\r\n                (this.data as LevelDataEventTriggerWave).waveGroup.push(levelDataWaveGroup);\r\n            });\r\n        } else {\r\n            (this.data as LevelDataEventTriggerWave).waveGroup = [];\r\n        }\r\n    }\r\n\r\n    public setWaveOffset(waveUUID: string, offset: Vec2) {\r\n        for (let waveGroup of this.waveGroup) {\r\n            if (waveGroup.prefab && waveGroup.prefab.uuid == waveUUID) {\r\n                waveGroup.wave.waveOffset = offset;\r\n                break;\r\n            }\r\n        }\r\n\r\n        (this.data as LevelDataEventTriggerWave).waveGroup = [];\r\n        this.waveGroup.forEach((waveGroup) => {\r\n            let levelDataWaveGroup = new LevelDataEventWaveGroup();\r\n            if (waveGroup.prefab) {\r\n                levelDataWaveGroup.wave.waveUUID = waveGroup.prefab.uuid;\r\n            }\r\n            levelDataWaveGroup.wave.waveOffset = waveGroup.wave.waveOffset;\r\n            levelDataWaveGroup.weight = waveGroup.weight;\r\n            (this.data as LevelDataEventTriggerWave).waveGroup.push(levelDataWaveGroup);\r\n        });\r\n    }\r\n\r\n    @property({\r\n        type: Enum(eLevelSpecialEvent),\r\n        visible () {\r\n            return this.type == LevelDataEventTriggerType.SpecialEvent;\r\n        }\r\n    })\r\n    public get eventType(): eLevelSpecialEvent {\r\n        return (this.data as LevelDataEventTriggerSpecialEvent).eventType;\r\n    }\r\n    public set eventType(value: eLevelSpecialEvent) {\r\n        (this.data as LevelDataEventTriggerSpecialEvent).eventType = value;\r\n    }\r\n\r\n    public copyFrom(source: LevelEditorEventTrigger) {\r\n        this._index = source._index;\r\n        this.type = source.type;\r\n        switch (this.type) {\r\n            case LevelDataEventTriggerType.Log:\r\n                this.message = source.message;\r\n                break;\r\n            case LevelDataEventTriggerType.Audio:\r\n                this.audio = source.audio;\r\n                break;\r\n            case LevelDataEventTriggerType.Wave:\r\n                this.waveGroup = source.waveGroup.map((waveGroup) => {\r\n                    let newWaveGroup = new LevelEditorWaveGroup();\r\n                    newWaveGroup.wave.wavePrefab = waveGroup.wave.wavePrefab;\r\n                    newWaveGroup.wave.waveOffset = waveGroup.wave.waveOffset;\r\n                    newWaveGroup.weight = waveGroup.weight;\r\n                    return newWaveGroup;\r\n                });\r\n                break;\r\n            case LevelDataEventTriggerType.SpecialEvent:\r\n                this.eventType = source.eventType;\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n    }            \r\n}\r\n\r\n@ccclass('LevelEditorEventUI')\r\n@executeInEditMode()\r\nexport class LevelEditorEventUI extends LevelEditorElemUI {\r\n    @property([LevelEditorCondition])\r\n    public conditions: LevelEditorCondition[] = [];\r\n    @property([LevelEditorEventTrigger])\r\n    public triggers: LevelEditorEventTrigger[] = [];\r\n\r\n    private sprite: Sprite | null = null;\r\n    onLoad() {\r\n        this.sprite = this.node.getComponent(Sprite) || this.node.addComponent(Sprite);\r\n        const eventSpriteUUID = 'aa8d57da-85f6-4319-8b4d-2a7864dccbe7';\r\n        assetManager.loadAny<ImageAsset>(eventSpriteUUID, (err:Error|null, asset: ImageAsset) => {\r\n            if (err) {\r\n                console.warn(err);\r\n                return;\r\n            }\r\n\r\n            this.sprite!.spriteFrame = SpriteFrame.createWithImage(asset);\r\n            const uiTransform = this.node.getComponent(UITransform) || this.node.addComponent(UITransform);\r\n            uiTransform.setContentSize(256, 256);\r\n        });\r\n    }\r\n\r\n    // 标记是否修改过\r\n    private _isDirty = false;\r\n    onFocusInEditor(): void {\r\n        this._isDirty = true;\r\n    }\r\n\r\n    private _waveMap: Map<string, Wave> = new Map();\r\n    private _wavePreviews: WavePreview[] = [];\r\n    public update(dt: number): void {\r\n        for (let i = 0; i < this.conditions.length; i++) {\r\n            const cond = this.conditions[i];\r\n            cond._index = i;\r\n            if (cond.type == LevelDataEventCondtionType.Wave \r\n                && (cond.data as LevelDataEventCondtionWave).targetElemID != \"\" \r\n                && cond._targetElem == null) {\r\n                const elems = this.node.scene.getComponentsInChildren(LevelEditorElemUI);\r\n                for (let elem of elems) {\r\n                    if (elem.elemID == (cond.data as LevelDataEventCondtionWave).targetElemID) {\r\n                        cond._targetElem = elem;\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        if (this._isDirty) {\r\n            this.syncWaveOffset();\r\n            // clear wave childrens \r\n            this.node.removeAllChildren();\r\n            this._waveMap.clear();\r\n\r\n            // 重新创建波次预览节点\r\n            this.triggers.forEach((trigger) => {\r\n                trigger.waveGroup.forEach((waveGroup) => {\r\n                    if (waveGroup.wave.wavePrefab) {\r\n                        const prefab = waveGroup.wave.wavePrefab;\r\n                        const waveNode = instantiate(prefab);\r\n                        this.node.addChild(waveNode);\r\n                        waveNode.setPosition(waveGroup.wave.waveOffset.x, waveGroup.wave.waveOffset.y);\r\n                        this.setupWave(waveNode.getComponent(Wave));\r\n                        this._waveMap.set(prefab.uuid, waveNode.getComponent(Wave)!);\r\n                    }\r\n                });\r\n            });\r\n            this._isDirty = false;\r\n        }\r\n    }\r\n\r\n    private syncWaveOffset() {\r\n        if (this._waveMap.size > 0) {\r\n            // 将位置修改同步回来\r\n            this._waveMap.forEach((wave, key) => {\r\n                if (!wave || !wave.node) return;\r\n\r\n                const waveOffset = wave.node.position;\r\n                for (let trigger of this.triggers) {\r\n                    trigger.setWaveOffset(key, new Vec2(waveOffset.x, waveOffset.y));\r\n                }\r\n            });\r\n        }\r\n    }\r\n\r\n    public initByLevelData(data: LevelDataEvent) {\r\n        super.initByLevelData(data)\r\n        if (data.conditions) {\r\n            for (let i = 0; i < data.conditions.length; i++) {\r\n                const condition = new LevelEditorCondition();\r\n                condition._index = i;\r\n                condition.data = data.conditions[i];\r\n                this.conditions.push(condition);\r\n            }\r\n        }\r\n        if (data.triggers) {\r\n            for (let i = 0; i < data.triggers.length; i++) {\r\n                const trigger = new LevelEditorEventTrigger();\r\n                trigger._index = i;\r\n                trigger.data = data.triggers[i];\r\n                if (trigger.data._type == LevelDataEventTriggerType.Audio) {\r\n                    let uuid = (trigger.data as LevelDataEventTriggerAudio).audioUUID;\r\n                    if (uuid != \"\") {\r\n                        assetManager.loadAny({uuid:uuid}, (err, audio:AudioClip) => {\r\n                            if (err) {\r\n                                console.error(\"LevelEditorEventUI initByLevelData load audio err\", err);\r\n                                return;\r\n                            }\r\n                            trigger._audio = audio;\r\n                        });\r\n                    }\r\n                }\r\n                if (trigger.data._type == LevelDataEventTriggerType.Wave) {\r\n                    let waveTrigger = trigger.data as LevelDataEventTriggerWave;\r\n                    for (let waveGroup of waveTrigger.waveGroup) { \r\n                        let editorWaveGroup = new LevelEditorWaveGroup();\r\n                        // console.log('waveGroup.wave.waveUUID: ', waveGroup);\r\n                        assetManager.loadAny({uuid:waveGroup.wave.waveUUID}, (err, prefab:Prefab) => {\r\n                            if (err) {\r\n                                console.error(\"LevelEditorEventUI initByLevelData load wave prefab err\", err);\r\n                                return;\r\n                            }\r\n                            editorWaveGroup.wave.wavePrefab = prefab;\r\n                        });\r\n\r\n                        editorWaveGroup.wave.waveOffset = waveGroup.wave.waveOffset;\r\n                        editorWaveGroup.weight = waveGroup.weight;\r\n                        trigger.waveGroup.push(editorWaveGroup);\r\n                    }\r\n                }\r\n                this.triggers.push(trigger);\r\n            }\r\n        }\r\n\r\n        this._isDirty = true;\r\n    }\r\n\r\n    public fillLevelData(data: LevelDataEvent) {\r\n        super.fillLevelData(data)\r\n        data.conditions = []\r\n        this.conditions.forEach((cond) => {\r\n            if (cond != null) {\r\n                data.conditions.push(cond.data);\r\n            }\r\n        })\r\n        this.syncWaveOffset();\r\n        this.triggers.forEach((trigger) => {\r\n            if (trigger != null) {\r\n                data.triggers.push(trigger.data);\r\n            }\r\n        })\r\n    }\r\n\r\n    public copyFrom(source: LevelEditorEventUI) {\r\n        this.elemID = source.elemID;\r\n        this.remark = source.remark;\r\n        this.node.position = source.node.position;\r\n        this.conditions = source.conditions.map((cond) => {\r\n            let newCond = new LevelEditorCondition();\r\n            newCond.copyFrom(cond);\r\n            return newCond;\r\n        });;\r\n        this.triggers = source.triggers.map((trigger) => {\r\n            let newTrigger = new LevelEditorEventTrigger();\r\n            newTrigger.copyFrom(trigger);\r\n            return newTrigger;\r\n        });;\r\n    }\r\n\r\n    private setupWave(wave: Wave|null): void {\r\n        if (wave == null) \r\n            return;\r\n\r\n        wave.setupInEditor();\r\n    }\r\n\r\n    private _isPlaying = false;\r\n    private _isActive = false;\r\n    private _positionBeforePlay: Vec3 = new Vec3();\r\n    public play(bPlay: boolean, progress: number) {\r\n        if (bPlay != this._isPlaying) {\r\n            this._isPlaying = bPlay;\r\n            if (bPlay) {\r\n                this.node.getPosition(this._positionBeforePlay);\r\n            }\r\n            else {\r\n                this.node.setPosition(this._positionBeforePlay);\r\n            }\r\n        }\r\n\r\n        if (bPlay) {\r\n            const posY = this._positionBeforePlay.y - progress;\r\n            if (posY <= GameConst.VIEWPORT_TOP) {\r\n                if (!this._isActive) {\r\n                    this.triggers.forEach((trigger) => {\r\n                        this.performTrigger(trigger.data, posY);\r\n                    })\r\n                    this._isActive = true;\r\n                }\r\n            }\r\n            this.node.setPosition(this._positionBeforePlay.x, posY, this._positionBeforePlay.z);\r\n        }\r\n        else {\r\n            if (this._isActive) {\r\n                this.resetPlay();\r\n                this._isActive = false;\r\n            }\r\n        }\r\n    }\r\n\r\n    private resetPlay() {\r\n        this._wavePreviews.forEach((wavePreview) => {\r\n            wavePreview.clearPreview();\r\n        });\r\n        this._wavePreviews = [];\r\n    }\r\n\r\n    private performTrigger(trigger: LevelDataEventTrigger, offsetY: number) {\r\n        switch (trigger._type) {\r\n            case LevelDataEventTriggerType.Log:\r\n                console.log(\"LevelEditorEventUI\", \"trigger log\", (trigger as LevelDataEventTriggerLog).message);\r\n                break;\r\n            case LevelDataEventTriggerType.Audio:\r\n                break;\r\n            case LevelDataEventTriggerType.Wave:\r\n                // Do Wave logic\r\n                this.triggerWave(trigger as LevelDataEventTriggerWave, offsetY);\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n    }\r\n\r\n    private triggerWave(triggerWave: LevelDataEventTriggerWave, offsetY: number) {\r\n        console.log('Trigger wave!! , ', offsetY);\r\n        const randomWeight = Math.floor(Math.random() * 100);\r\n        let curWeight = 0;\r\n        let selectedWaveGroup: LevelDataEventWaveGroup | null = null;\r\n        for (let waveGroup of triggerWave.waveGroup) {\r\n            curWeight += waveGroup.weight;\r\n            if (randomWeight <= curWeight) {\r\n                selectedWaveGroup = waveGroup;\r\n                break;\r\n            }\r\n        }\r\n        if (selectedWaveGroup == null) {\r\n            return;\r\n        }\r\n        let wave = this._waveMap.get(selectedWaveGroup.wave.waveUUID);\r\n        if (wave) {\r\n            const position = this.node.position;\r\n            const wavePreview = wave.node.getComponentInChildren(WavePreview);\r\n            if (wavePreview) {\r\n                wavePreview.triggerPreview(position.x, offsetY);\r\n                this._wavePreviews.push(wavePreview);\r\n            }\r\n        } \r\n    }\r\n}"]}