{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerWave.ts"], "names": ["LevelDataEventWave", "LevelDataEventWaveGroup", "LevelDataEventTriggerWave", "instantiate", "Vec2", "MyApp", "Wave", "GameIns", "LevelDataEventTrigger", "LevelDataEventTriggerType", "waveUUID", "waveOffset", "fromJSON", "obj", "Object", "assign", "toJSON", "wave", "weight", "constructor", "waveGroup", "_isTriggered", "_selectedWaveGroup", "onInit", "length", "totalWeight", "for<PERSON>ach", "randomWeight", "Math", "floor", "battleManager", "random", "curWeight", "onTrigger", "x", "y", "offset", "path", "resMgr", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultBundleName", "load", "err", "prefab", "console", "error", "waveComp", "getComponent", "waveManager", "addWaveByLevel", "isWaveSpawned", "isWaveSpawnCompleted", "isWaveCleared", "isWaveEnemyDefeated", "isWaveTriggered", "_type", "JSON", "parse", "map", "newWaveGroup"], "mappings": ";;;0KAMaA,kB,EAgBAC,uB,EAiBAC,yB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAvCQC,MAAAA,W,OAAAA,W;AAAqBC,MAAAA,I,OAAAA,I;;AACjCC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,qB,iBAAAA,qB;AAAuBC,MAAAA,yB,iBAAAA,yB;;;;;;;;;oCAEnBT,kB,GAAN,MAAMA,kBAAN,CAAyB;AAAA;AAAA,eACrBU,QADqB,GACF,EADE;AAAA,eAErBC,UAFqB,GAEF,IAAIP,IAAJ,EAFE;AAAA;;AAIrBQ,QAAAA,QAAQ,CAACC,GAAD,EAAiB;AAC5BC,UAAAA,MAAM,CAACC,MAAP,CAAc,IAAd,EAAoBF,GAApB;AACH;;AAEMG,QAAAA,MAAM,GAAQ;AACjB,iBAAO;AACHN,YAAAA,QAAQ,EAAE,KAAKA,QADZ;AAEHC,YAAAA,UAAU,EAAE,KAAKA;AAFd,WAAP;AAIH;;AAb2B,O;;yCAgBnBV,uB,GAAN,MAAMA,uBAAN,CAA8B;AAAA;AAAA,eAC1BgB,IAD0B,GACC,IAAIjB,kBAAJ,EADD;AAAA,eAE1BkB,MAF0B,GAET,EAFS;AAAA;;AAI1BN,QAAAA,QAAQ,CAACC,GAAD,EAAiB;AAC5B,eAAKI,IAAL,CAAUL,QAAV,CAAmBC,GAAG,CAACI,IAAvB;AACA,eAAKC,MAAL,GAAcL,GAAG,CAACK,MAAlB;AACH;;AAEMF,QAAAA,MAAM,GAAQ;AACjB,iBAAO;AACHC,YAAAA,IAAI,EAAE,KAAKA,IAAL,CAAUD,MAAV,EADH;AAEHE,YAAAA,MAAM,EAAE,KAAKA;AAFV,WAAP;AAIH;;AAdgC,O;;2CAiBxBhB,yB,GAAN,MAAMA,yBAAN;AAAA;AAAA,0DAA8D;AAKjEiB,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,sEAA0Bb,IAAhC;AADU,eAJPc,SAIO,GAJgC,EAIhC;AAAA,eAHNC,YAGM,GAHkB,KAGlB;AAAA,eAFNC,kBAEM,GAF+C,IAE/C;AAEb;;AAEMC,QAAAA,MAAM,GAAG;AACZ;AACA,cAAI,KAAKH,SAAL,CAAeI,MAAf,GAAwB,CAA5B,EAA+B;AAC3B,gBAAIC,WAAW,GAAG,CAAlB;AACA,iBAAKL,SAAL,CAAeM,OAAf,CAAuBN,SAAS,IAAI;AAChCK,cAAAA,WAAW,IAAIL,SAAS,CAACF,MAAzB;AACH,aAFD;AAIA,gBAAIS,YAAY,GAAGC,IAAI,CAACC,KAAL,CAAW;AAAA;AAAA,oCAAQC,aAAR,CAAsBC,MAAtB,KAAiCN,WAA5C,CAAnB;AACA,gBAAIO,SAAS,GAAG,CAAhB;;AAEA,iBAAK,IAAIZ,SAAT,IAAsB,KAAKA,SAA3B,EAAsC;AAClCY,cAAAA,SAAS,IAAIZ,SAAS,CAACF,MAAvB;;AACA,kBAAIS,YAAY,IAAIK,SAApB,EAA+B;AAC3B,qBAAKV,kBAAL,GAA0BF,SAA1B;AACA;AACH;AACJ;AACJ;AACJ;;AAEMa,QAAAA,SAAS,CAACC,CAAD,EAAYC,CAAZ,EAAuB;AACnC,gBAAMzB,QAAQ,GAAG,KAAKY,kBAAL,CAAyBL,IAAzB,CAA8BP,QAA/C;AACA,gBAAM0B,MAAM,GAAG,KAAKd,kBAAL,CAAyBL,IAAzB,CAA8BN,UAA7C;;AACA,cAAID,QAAJ,EAAc;AACV,kBAAM2B,IAAI,GAAG;AAAA;AAAA,gCAAMC,MAAN,CAAaC,YAAb,CAA0B;AAAA;AAAA,gCAAMD,MAAN,CAAaE,iBAAvC,EAA0D9B,QAA1D,CAAb;AACA;AAAA;AAAA,gCAAM4B,MAAN,CAAaG,IAAb,CAAkBJ,IAAlB,EAAwB,CAACK,GAAD,EAAoBC,MAApB,KAAuC;AAC3D,kBAAID,GAAJ,EAAS;AACLE,gBAAAA,OAAO,CAACC,KAAR,CAAc,2BAAd,EAA2C,8BAA3C,EAA2EH,GAA3E;AACA;AACH;;AACD,oBAAMI,QAAQ,GAAG3C,WAAW,CAACwC,MAAD,CAAX,CAAoBI,YAApB;AAAA;AAAA,+BAAjB;AACA;AAAA;AAAA,sCAAQC,WAAR,CAAoBC,cAApB,CAAmCH,QAAnC,EAA8CV,MAAM,CAACF,CAAP,GAAWA,CAAzD,EAA4DE,MAAM,CAACD,CAAP,GAAWA,CAAvE;AACH,aAPD;AAQH;;AAED,eAAKd,YAAL,GAAoB,IAApB;AACH;;AAEM6B,QAAAA,aAAa,GAAY;AAC5B,cAAI,CAAC,KAAK7B,YAAV,EAAwB,OAAO,KAAP;;AAExB,cAAI,KAAKC,kBAAL,CAAyBL,IAAzB,CAA8BP,QAAlC,EAA4C;AACxC,mBAAO;AAAA;AAAA,oCAAQsC,WAAR,CAAoBG,oBAApB,CAAyC,KAAK7B,kBAAL,CAAyBL,IAAzB,CAA8BP,QAAvE,CAAP;AACH;;AAED,iBAAO,IAAP;AACH;;AAEM0C,QAAAA,aAAa,GAAY;AAC5B,cAAI,CAAC,KAAK/B,YAAV,EAAwB,OAAO,KAAP;;AAExB,cAAI,KAAKC,kBAAL,CAAyBL,IAAzB,CAA8BP,QAAlC,EAA4C;AACxC,mBAAO;AAAA;AAAA,oCAAQsC,WAAR,CAAoBK,mBAApB,CAAwC,KAAK/B,kBAAL,CAAyBL,IAAzB,CAA8BP,QAAtE,CAAP;AACH;;AAED,iBAAO,IAAP;AACH;;AAEM4C,QAAAA,eAAe,GAAY;AAC9B,iBAAO,KAAKjC,YAAZ;AACH;;AAEMT,QAAAA,QAAQ,CAACC,GAAD,EAAiB;AAC5B,eAAK0C,KAAL,GAAa1C,GAAG,CAAC0C,KAAjB;;AAEA,cAAI,CAAC1C,GAAG,CAACO,SAAT,EAAoB;AAChBP,YAAAA,GAAG,CAACO,SAAJ,GAAgB,EAAhB;AACH,WAFD,MAEO,IAAI,OAAOP,GAAG,CAACO,SAAX,KAA0B,QAA9B,EAAwC;AAC3CP,YAAAA,GAAG,CAACO,SAAJ,GAAgBoC,IAAI,CAACC,KAAL,CAAW5C,GAAG,CAACO,SAAf,CAAhB;AACH;;AAED,eAAKA,SAAL,GAAiBP,GAAG,CAACO,SAAJ,CAAcsC,GAAd,CAAmBtC,SAAD,IAAoB;AACnD,gBAAIuC,YAAY,GAAG,IAAI1D,uBAAJ,EAAnB;AACA0D,YAAAA,YAAY,CAAC/C,QAAb,CAAsBQ,SAAtB;AACA,mBAAOuC,YAAP;AACH,WAJgB,CAAjB;AAKH;;AAEM3C,QAAAA,MAAM,GAAQ;AACjB;AACA,iBAAO;AACHuC,YAAAA,KAAK,EAAE,KAAKA,KADT;AAEHnC,YAAAA,SAAS,EAAE,KAAKA,SAAL,CAAesC,GAAf,CAAoBtC,SAAD,IAAeA,SAAS,CAACJ,MAAV,EAAlC;AAFR,WAAP;AAIH;;AA9FgE,O", "sourcesContent": ["import { _decorator, instantiate, Prefab, Vec2 } from \"cc\";\r\nimport { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\r\nimport { Wave } from \"../../game/wave/Wave\";\r\nimport { GameIns } from \"../../game/GameIns\";\r\nimport { LevelDataEventTrigger, LevelDataEventTriggerType } from \"./LevelDataEventTrigger\";\r\n\r\nexport class LevelDataEventWave {\r\n    public waveUUID: string = \"\";\r\n    public waveOffset: Vec2 = new Vec2();\r\n\r\n    public fromJSON(obj: any): void {\r\n        Object.assign(this, obj);\r\n    }\r\n\r\n    public toJSON(): any {\r\n        return {\r\n            waveUUID: this.waveUUID,\r\n            waveOffset: this.waveOffset,\r\n        };\r\n    }\r\n}\r\n\r\nexport class LevelDataEventWaveGroup {\r\n    public wave: LevelDataEventWave = new LevelDataEventWave();\r\n    public weight: number = 50;\r\n\r\n    public fromJSON(obj: any): void {\r\n        this.wave.fromJSON(obj.wave);\r\n        this.weight = obj.weight;\r\n    }\r\n\r\n    public toJSON(): any {\r\n        return {\r\n            wave: this.wave.toJSON(),\r\n            weight: this.weight,\r\n        };\r\n    }\r\n}\r\n\r\nexport class LevelDataEventTriggerWave extends LevelDataEventTrigger {\r\n    public waveGroup: LevelDataEventWaveGroup[] = [];\r\n    private _isTriggered: boolean = false;\r\n    private _selectedWaveGroup: LevelDataEventWaveGroup | null = null;\r\n    \r\n    constructor() {\r\n        super(LevelDataEventTriggerType.Wave);\r\n    }\r\n    \r\n    public onInit() {\r\n        // 提前创建好wave，但不执行\r\n        if (this.waveGroup.length > 0) {\r\n            let totalWeight = 0;\r\n            this.waveGroup.forEach(waveGroup => {\r\n                totalWeight += waveGroup.weight;\r\n            });\r\n\r\n            let randomWeight = Math.floor(GameIns.battleManager.random() * totalWeight);\r\n            let curWeight = 0;\r\n            \r\n            for (let waveGroup of this.waveGroup) {\r\n                curWeight += waveGroup.weight;\r\n                if (randomWeight <= curWeight) {\r\n                    this._selectedWaveGroup = waveGroup;\r\n                    break;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    public onTrigger(x: number, y: number) {\r\n        const waveUUID = this._selectedWaveGroup!.wave.waveUUID;\r\n        const offset = this._selectedWaveGroup!.wave.waveOffset;\r\n        if (waveUUID) {\r\n            const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, waveUUID)\r\n            MyApp.resMgr.load(path, (err: Error | null, prefab: Prefab) => {\r\n                if (err) {\r\n                    console.error('LevelDataEventTriggerWave', \" onInit load wave prefab err\", err);\r\n                    return;\r\n                }\r\n                const waveComp = instantiate(prefab).getComponent(Wave);\r\n                GameIns.waveManager.addWaveByLevel(waveComp!, offset.x + x, offset.y + y);\r\n            });\r\n        }\r\n\r\n        this._isTriggered = true;\r\n    }\r\n\r\n    public isWaveSpawned(): boolean {\r\n        if (!this._isTriggered) return false;\r\n\r\n        if (this._selectedWaveGroup!.wave.waveUUID) {\r\n            return GameIns.waveManager.isWaveSpawnCompleted(this._selectedWaveGroup!.wave.waveUUID);\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    public isWaveCleared(): boolean {\r\n        if (!this._isTriggered) return false;\r\n\r\n        if (this._selectedWaveGroup!.wave.waveUUID) {\r\n            return GameIns.waveManager.isWaveEnemyDefeated(this._selectedWaveGroup!.wave.waveUUID);\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    public isWaveTriggered(): boolean {\r\n        return this._isTriggered;\r\n    }\r\n\r\n    public fromJSON(obj: any): void {\r\n        this._type = obj._type;\r\n\r\n        if (!obj.waveGroup) {\r\n            obj.waveGroup = [];\r\n        } else if (typeof(obj.waveGroup) === 'string') {\r\n            obj.waveGroup = JSON.parse(obj.waveGroup);\r\n        }\r\n\r\n        this.waveGroup = obj.waveGroup.map((waveGroup: any) => {\r\n            let newWaveGroup = new LevelDataEventWaveGroup();\r\n            newWaveGroup.fromJSON(waveGroup);\r\n            return newWaveGroup;\r\n        });\r\n    }\r\n\r\n    public toJSON(): any {\r\n        // avoid private properties\r\n        return {\r\n            _type: this._type,\r\n            waveGroup: this.waveGroup.map((waveGroup) => waveGroup.toJSON()),\r\n        };\r\n    }\r\n}\r\n\r\n"]}