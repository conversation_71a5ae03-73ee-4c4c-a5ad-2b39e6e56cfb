{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/base/Entity.ts"], "names": ["_decorator", "Component", "GameConst", "ccclass", "eEntityTag", "Entity", "isDead", "m_comps", "Map", "m_tags", "None", "init", "initComps", "updateGameLogic", "dt", "for<PERSON>ach", "comp", "addTag", "tag", "removeTag", "hasTag", "clearTags", "getComp", "type", "get", "getComps", "addComp", "set", "removeComp", "remove", "delete", "removeAllComp", "Array", "from", "values", "clear", "removeOther<PERSON>omps", "keepComp", "onCollide", "collision", "onOutScreen", "isInScreen", "isPointInBattleView", "node", "position", "getAttack"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;AAGZC,MAAAA,S,iBAAAA,S;;;;;;;;;OAEH;AAAEC,QAAAA;AAAF,O,GAAcH,U;;4BAERI,U,0BAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;eAAAA,U;;;yBAUSC,M,WADpBF,OAAO,CAAC,QAAD,C,gBAAR,MACqBE,MADrB,SACoCJ,SADpC,CAC8C;AAAA;AAAA;AAAA,eAEnCK,MAFmC,GAEjB,KAFiB;AAEV;AAFU,eAI1CC,OAJ0C,GAIhC,IAAIC,GAAJ,EAJgC;AAIH;AAJG,eAK1CC,MAL0C,GAKrBL,UAAU,CAACM,IALU;AAAA;;AAO1CC,QAAAA,IAAI,GAAG;AACH,eAAKC,SAAL;AACH;;AAEDC,QAAAA,eAAe,CAACC,EAAD,EAAY,CAAE;;AAE7BF,QAAAA,SAAS,GAAG;AACR,eAAKL,OAAL,CAAaQ,OAAb,CAAsBC,IAAD,IAAU;AAC3BA,YAAAA,IAAI,CAACL,IAAL,CAAU,IAAV;AACH,WAFD;AAGH;;AAEDM,QAAAA,MAAM,CAACC,GAAD,EAAkB;AACpB,eAAKT,MAAL,IAAeS,GAAf;AACH;;AAEDC,QAAAA,SAAS,CAACD,GAAD,EAAkB;AACvB,eAAKT,MAAL,IAAe,CAACS,GAAhB;AACH;;AAEDE,QAAAA,MAAM,CAACF,GAAD,EAA2B;AAC7B,iBAAO,CAAC,KAAKT,MAAL,GAAcS,GAAf,MAAwB,CAA/B;AACH;;AAEDG,QAAAA,SAAS,GAAG;AACR,eAAKZ,MAAL,GAAcL,UAAU,CAACM,IAAzB;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIY,QAAAA,OAAO,CAACC,IAAD,EAAc;AACjB,iBAAO,KAAKhB,OAAL,CAAaiB,GAAb,CAAiBD,IAAjB,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIE,QAAAA,QAAQ,CAACF,IAAD,EAAe;AACnB,iBAAO,KAAKhB,OAAL,CAAaiB,GAAb,CAAiBD,IAAjB,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIG,QAAAA,OAAO,CAACH,IAAD,EAAeP,IAAf,EAA+B;AAClC,eAAKT,OAAL,CAAaoB,GAAb,CAAiBJ,IAAjB,EAAuBP,IAAvB;AACA,iBAAOA,IAAP;AACH;AAED;AACJ;AACA;AACA;;;AACIY,QAAAA,UAAU,CAACL,IAAD,EAAe;AACrB,gBAAMP,IAAI,GAAG,KAAKM,OAAL,CAAaC,IAAb,CAAb;;AACA,cAAIP,IAAJ,EAAU;AACNA,YAAAA,IAAI,CAACa,MAAL;AACH;;AACD,eAAKtB,OAAL,CAAauB,MAAb,CAAoBP,IAApB;AACH;AAED;AACJ;AACA;;;AACIQ,QAAAA,aAAa,GAAG;AACZ,cAAI,KAAKxB,OAAL,IAAgB,IAApB,EAA0B;AACtByB,YAAAA,KAAK,CAACC,IAAN,CAAW,KAAK1B,OAAL,CAAa2B,MAAb,EAAX,EAAkCnB,OAAlC,CAA2CC,IAAD,IAAU;AAChDA,cAAAA,IAAI,CAACa,MAAL;AACH,aAFD;AAGA,iBAAKtB,OAAL,CAAa4B,KAAb;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,gBAAgB,CAACC,QAAD,EAAqB;AACjC,cAAI,KAAK9B,OAAL,IAAgB,IAApB,EAA0B;AACtB,iBAAKA,OAAL,CAAaQ,OAAb,CAAqB,CAACC,IAAD,EAAOO,IAAP,KAAgB;AACjC,kBAAIP,IAAI,KAAKqB,QAAb,EAAuB;AACnB,qBAAK9B,OAAL,CAAauB,MAAb,CAAoBP,IAApB;AACH;AACJ,aAJD;AAKH;AACJ;;AAEDe,QAAAA,SAAS,CAACC,SAAD,EAAuB,CAE/B;;AAEDC,QAAAA,WAAW,GAAG,CAEb,CA5GyC,CA8G1C;AACA;;;AACAC,QAAAA,UAAU,GAAY;AAClB,iBAAO;AAAA;AAAA,sCAAUC,mBAAV,CAA8B,KAAKC,IAAL,CAAUC,QAAxC,CAAP;AACH;;AAEDC,QAAAA,SAAS,GAAU;AACf,iBAAO,CAAP;AACH;;AAtHyC,O", "sourcesContent": ["import { _decorator, Component } from 'cc';\r\nimport BaseComp from './BaseComp';\r\nimport FCollider from '../../collider-system/FCollider';\r\nimport { GameConst } from 'db://assets/scripts/core/base/GameConst';\r\n\r\nconst { ccclass } = _decorator;\r\n\r\nexport enum eEntityTag {\r\n    None = 0,\r\n    Player = 1,\r\n    Enemy = 2,\r\n    PlayerBullet = 4,\r\n    EnemyBullet = 8,\r\n    Boss = 16,\r\n}\r\n\r\n@ccclass('Entity')\r\nexport default class Entity extends Component {\r\n\r\n    public isDead: boolean = false; // 是否死亡\r\n    \r\n    m_comps = new Map<string, BaseComp>(); // 存储组件的 Map\r\n    m_tags: eEntityTag = eEntityTag.None;\r\n\r\n    init() {\r\n        this.initComps()\r\n    }\r\n\r\n    updateGameLogic(dt:number) {}\r\n\r\n    initComps() {\r\n        this.m_comps.forEach((comp) => {\r\n            comp.init(this);\r\n        });\r\n    }\r\n\r\n    addTag(tag: eEntityTag) {\r\n        this.m_tags |= tag;\r\n    }\r\n\r\n    removeTag(tag: eEntityTag) {\r\n        this.m_tags &= ~tag;\r\n    }\r\n\r\n    hasTag(tag: eEntityTag): boolean {\r\n        return (this.m_tags & tag) !== 0;\r\n    }\r\n\r\n    clearTags() {\r\n        this.m_tags = eEntityTag.None;\r\n    }\r\n\r\n    /**\r\n     * 获取指定类型的组件\r\n     * @param {string} type 组件类型\r\n     * @returns {any} 组件实例\r\n     */\r\n    getComp(type:string) {\r\n        return this.m_comps.get(type);\r\n    }\r\n\r\n    /**\r\n     * 获取指定类型的所有组件\r\n     * @param {string} type 组件类型\r\n     * @returns {any} 组件实例\r\n     */\r\n    getComps(type: string) {\r\n        return this.m_comps.get(type);\r\n    }\r\n\r\n    /**\r\n     * 添加组件\r\n     * @param {string} type 组件类型\r\n     * @param {any} comp 组件实例\r\n     * @returns {any} 添加的组件实例\r\n     */\r\n    addComp(type: string, comp: BaseComp) {\r\n        this.m_comps.set(type, comp);\r\n        return comp;\r\n    }\r\n\r\n    /**\r\n     * 移除指定类型的组件\r\n     * @param {string} type 组件类型\r\n     */\r\n    removeComp(type: string) {\r\n        const comp = this.getComp(type);\r\n        if (comp) {\r\n            comp.remove();\r\n        }\r\n        this.m_comps.delete(type);\r\n    }\r\n\r\n    /**\r\n     * 移除所有组件\r\n     */\r\n    removeAllComp() {\r\n        if (this.m_comps != null) {\r\n            Array.from(this.m_comps.values()).forEach((comp) => {\r\n                comp.remove();\r\n            });\r\n            this.m_comps.clear();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 移除除指定组件外的其他组件\r\n     * @param {BaseComp} keepComp 要保留的组件\r\n     */\r\n    removeOtherComps(keepComp: BaseComp) {\r\n        if (this.m_comps != null) {\r\n            this.m_comps.forEach((comp, type) => {\r\n                if (comp !== keepComp) {\r\n                    this.m_comps.delete(type);\r\n                }\r\n            });\r\n        }\r\n    }\r\n\r\n    onCollide(collision: FCollider) {\r\n        \r\n    }\r\n\r\n    onOutScreen() {\r\n        \r\n    }\r\n\r\n    // 是否在游戏世界范围内(比屏幕宽一点)\r\n    // 注意这里只用了点来判断，正常来说要知道宽高，用Rect来判断更合理。\r\n    isInScreen(): boolean {\r\n        return GameConst.isPointInBattleView(this.node.position);\r\n    }\r\n\r\n    getAttack():number {\r\n        return 1;\r\n    }\r\n}"]}