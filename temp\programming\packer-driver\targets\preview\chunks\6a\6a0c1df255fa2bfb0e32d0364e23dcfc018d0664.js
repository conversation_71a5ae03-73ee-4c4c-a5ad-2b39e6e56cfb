System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10", "__unresolved_11", "__unresolved_12"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Enum, Vec3, CCString, assetManager, UITransform, Sprite, SpriteFrame, instantiate, Vec2, AudioClip, LevelDataEventTriggerType, LevelDataEventTriggerLog, LevelDataEventWaveGroup, eLevelSpecialEvent, newTrigger, LevelDataEventCondtionType, Wave, WavePreview, LevelEditorElemUI, LevelEditorWaveGroup, LevelEditorCondition, GameConst, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _class, _class2, _dec7, _dec8, _dec9, _dec10, _class4, _class5, _descriptor, _descriptor2, _crd, ccclass, property, executeInEditMode, LevelEditorEventTrigger, LevelEditorEventUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _reportPossibleCrUseOfLevelDataEvent(extras) {
    _reporterNs.report("LevelDataEvent", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventTrigger(extras) {
    _reporterNs.report("LevelDataEventTrigger", "db://assets/bundles/common/script/leveldata/trigger/LevelDataEventTrigger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventTriggerType(extras) {
    _reporterNs.report("LevelDataEventTriggerType", "db://assets/bundles/common/script/leveldata/trigger/LevelDataEventTrigger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventTriggerLog(extras) {
    _reporterNs.report("LevelDataEventTriggerLog", "db://assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerLog", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventTriggerAudio(extras) {
    _reporterNs.report("LevelDataEventTriggerAudio", "db://assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerAudio", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventTriggerWave(extras) {
    _reporterNs.report("LevelDataEventTriggerWave", "db://assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerWave", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventWaveGroup(extras) {
    _reporterNs.report("LevelDataEventWaveGroup", "db://assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerWave", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventTriggerSpecialEvent(extras) {
    _reporterNs.report("LevelDataEventTriggerSpecialEvent", "db://assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerSpecialEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeLevelSpecialEvent(extras) {
    _reporterNs.report("eLevelSpecialEvent", "db://assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerSpecialEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfnewTrigger(extras) {
    _reporterNs.report("newTrigger", "db://assets/bundles/common/script/leveldata/trigger/newTrigger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventCondtionType(extras) {
    _reporterNs.report("LevelDataEventCondtionType", "db://assets/bundles/common/script/leveldata/condition/LevelDataEventCondtion", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventCondtionWave(extras) {
    _reporterNs.report("LevelDataEventCondtionWave", "db://assets/bundles/common/script/leveldata/condition/LevelDataEventCondtionWave", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWave(extras) {
    _reporterNs.report("Wave", "db://assets/bundles/common/script/game/wave/Wave", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWavePreview(extras) {
    _reporterNs.report("WavePreview", "./preview/WavePreview", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelEditorElemUI(extras) {
    _reporterNs.report("LevelEditorElemUI", "./LevelEditorElemUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelEditorWaveGroup(extras) {
    _reporterNs.report("LevelEditorWaveGroup", "./LevelEditorWaveParam", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelEditorCondition(extras) {
    _reporterNs.report("LevelEditorCondition", "./LevelEditorCondition", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../../scripts/core/base/GameConst", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Enum = _cc.Enum;
      Vec3 = _cc.Vec3;
      CCString = _cc.CCString;
      assetManager = _cc.assetManager;
      UITransform = _cc.UITransform;
      Sprite = _cc.Sprite;
      SpriteFrame = _cc.SpriteFrame;
      instantiate = _cc.instantiate;
      Vec2 = _cc.Vec2;
      AudioClip = _cc.AudioClip;
    }, function (_unresolved_2) {
      LevelDataEventTriggerType = _unresolved_2.LevelDataEventTriggerType;
    }, function (_unresolved_3) {
      LevelDataEventTriggerLog = _unresolved_3.LevelDataEventTriggerLog;
    }, function (_unresolved_4) {
      LevelDataEventWaveGroup = _unresolved_4.LevelDataEventWaveGroup;
    }, function (_unresolved_5) {
      eLevelSpecialEvent = _unresolved_5.eLevelSpecialEvent;
    }, function (_unresolved_6) {
      newTrigger = _unresolved_6.newTrigger;
    }, function (_unresolved_7) {
      LevelDataEventCondtionType = _unresolved_7.LevelDataEventCondtionType;
    }, function (_unresolved_8) {
      Wave = _unresolved_8.Wave;
    }, function (_unresolved_9) {
      WavePreview = _unresolved_9.WavePreview;
    }, function (_unresolved_10) {
      LevelEditorElemUI = _unresolved_10.LevelEditorElemUI;
    }, function (_unresolved_11) {
      LevelEditorWaveGroup = _unresolved_11.LevelEditorWaveGroup;
    }, function (_unresolved_12) {
      LevelEditorCondition = _unresolved_12.LevelEditorCondition;
    }, function (_unresolved_13) {
      GameConst = _unresolved_13.GameConst;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "9fa8bQG+gZMmpqlZsjy9rl1", "LevelEditorEventUI", undefined);

      __checkObsolete__(['_decorator', 'Enum', 'CCFloat', 'Component', 'JsonAsset', 'Node', 'Prefab', 'Slider', 'Vec3', 'ValueType', 'CCBoolean', 'CCString', 'ImageAsset', 'resources', 'assetManager', 'UITransform', 'Sprite', 'SpriteFrame', 'SpriteAtlas', 'math', 'instantiate', 'Vec2', 'CCInteger', 'AudioClip', 'BitMask']);

      ({
        ccclass,
        property,
        executeInEditMode
      } = _decorator);

      _export("LevelEditorEventTrigger", LevelEditorEventTrigger = (_dec = ccclass('LevelEditorEventTrigger'), _dec2 = property({
        type: Enum(_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
          error: Error()
        }), LevelDataEventTriggerType) : LevelDataEventTriggerType)
      }), _dec3 = property({
        type: CCString,

        visible() {
          return this.type == (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
            error: Error()
          }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Log;
        }

      }), _dec4 = property({
        type: AudioClip,

        visible() {
          return this.type == (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
            error: Error()
          }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Audio;
        }

      }), _dec5 = property({
        type: [_crd && LevelEditorWaveGroup === void 0 ? (_reportPossibleCrUseOfLevelEditorWaveGroup({
          error: Error()
        }), LevelEditorWaveGroup) : LevelEditorWaveGroup],
        displayName: "波次组随机",

        visible() {
          return this.type == (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
            error: Error()
          }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Wave;
        }

      }), _dec6 = property({
        type: Enum(_crd && eLevelSpecialEvent === void 0 ? (_reportPossibleCrUseOfeLevelSpecialEvent({
          error: Error()
        }), eLevelSpecialEvent) : eLevelSpecialEvent),

        visible() {
          return this.type == (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
            error: Error()
          }), LevelDataEventTriggerType) : LevelDataEventTriggerType).SpecialEvent;
        }

      }), _dec(_class = (_class2 = class LevelEditorEventTrigger {
        constructor() {
          this._index = 0;
          this.data = new (_crd && LevelDataEventTriggerLog === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerLog({
            error: Error()
          }), LevelDataEventTriggerLog) : LevelDataEventTriggerLog)();
          this._audio = null;
          this._waveGroup = [];
        }

        get type() {
          return this.data._type;
        }

        set type(value) {
          if (this.data._type != value) {
            this.data = (_crd && newTrigger === void 0 ? (_reportPossibleCrUseOfnewTrigger({
              error: Error()
            }), newTrigger) : newTrigger)({
              _type: value
            });
          }
        }

        get message() {
          return this.data.message;
        }

        set message(value) {
          this.data.message = value;
        }

        get audio() {
          return this._audio;
        }

        set audio(value) {
          this._audio = value;

          if (value) {
            this.data.audioUUID = value.uuid;
          } else {
            this.data.audioUUID = "";
          }
        }

        get waveGroup() {
          return this._waveGroup;
        }

        set waveGroup(value) {
          this._waveGroup = value;

          if (value) {
            this.data.waveGroup = [];
            value.forEach(waveGroup => {
              var levelDataWaveGroup = new (_crd && LevelDataEventWaveGroup === void 0 ? (_reportPossibleCrUseOfLevelDataEventWaveGroup({
                error: Error()
              }), LevelDataEventWaveGroup) : LevelDataEventWaveGroup)();

              if (waveGroup.prefab) {
                levelDataWaveGroup.wave.waveUUID = waveGroup.prefab.uuid;
              }

              levelDataWaveGroup.wave.waveOffset = waveGroup.wave.waveOffset;
              levelDataWaveGroup.weight = waveGroup.weight;
              this.data.waveGroup.push(levelDataWaveGroup);
            });
          } else {
            this.data.waveGroup = [];
          }
        }

        setWaveOffset(waveUUID, offset) {
          for (var waveGroup of this.waveGroup) {
            if (waveGroup.prefab && waveGroup.prefab.uuid == waveUUID) {
              waveGroup.wave.waveOffset = offset;
              break;
            }
          }

          this.data.waveGroup = [];
          this.waveGroup.forEach(waveGroup => {
            var levelDataWaveGroup = new (_crd && LevelDataEventWaveGroup === void 0 ? (_reportPossibleCrUseOfLevelDataEventWaveGroup({
              error: Error()
            }), LevelDataEventWaveGroup) : LevelDataEventWaveGroup)();

            if (waveGroup.prefab) {
              levelDataWaveGroup.wave.waveUUID = waveGroup.prefab.uuid;
            }

            levelDataWaveGroup.wave.waveOffset = waveGroup.wave.waveOffset;
            levelDataWaveGroup.weight = waveGroup.weight;
            this.data.waveGroup.push(levelDataWaveGroup);
          });
        }

        get eventType() {
          return this.data.eventType;
        }

        set eventType(value) {
          this.data.eventType = value;
        }

        copyFrom(source) {
          this._index = source._index;
          this.type = source.type;

          switch (this.type) {
            case (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
              error: Error()
            }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Log:
              this.message = source.message;
              break;

            case (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
              error: Error()
            }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Audio:
              this.audio = source.audio;
              break;

            case (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
              error: Error()
            }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Wave:
              this.waveGroup = source.waveGroup.map(waveGroup => {
                var newWaveGroup = new (_crd && LevelEditorWaveGroup === void 0 ? (_reportPossibleCrUseOfLevelEditorWaveGroup({
                  error: Error()
                }), LevelEditorWaveGroup) : LevelEditorWaveGroup)();
                newWaveGroup.wave.wavePrefab = waveGroup.wave.wavePrefab;
                newWaveGroup.wave.waveOffset = waveGroup.wave.waveOffset;
                newWaveGroup.weight = waveGroup.weight;
                return newWaveGroup;
              });
              break;

            case (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
              error: Error()
            }), LevelDataEventTriggerType) : LevelDataEventTriggerType).SpecialEvent:
              this.eventType = source.eventType;
              break;

            default:
              break;
          }
        }

      }, (_applyDecoratedDescriptor(_class2.prototype, "type", [_dec2], Object.getOwnPropertyDescriptor(_class2.prototype, "type"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "message", [_dec3], Object.getOwnPropertyDescriptor(_class2.prototype, "message"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "audio", [_dec4], Object.getOwnPropertyDescriptor(_class2.prototype, "audio"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "waveGroup", [_dec5], Object.getOwnPropertyDescriptor(_class2.prototype, "waveGroup"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "eventType", [_dec6], Object.getOwnPropertyDescriptor(_class2.prototype, "eventType"), _class2.prototype)), _class2)) || _class));

      _export("LevelEditorEventUI", LevelEditorEventUI = (_dec7 = ccclass('LevelEditorEventUI'), _dec8 = executeInEditMode(), _dec9 = property([_crd && LevelEditorCondition === void 0 ? (_reportPossibleCrUseOfLevelEditorCondition({
        error: Error()
      }), LevelEditorCondition) : LevelEditorCondition]), _dec10 = property([LevelEditorEventTrigger]), _dec7(_class4 = _dec8(_class4 = (_class5 = class LevelEditorEventUI extends (_crd && LevelEditorElemUI === void 0 ? (_reportPossibleCrUseOfLevelEditorElemUI({
        error: Error()
      }), LevelEditorElemUI) : LevelEditorElemUI) {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "conditions", _descriptor, this);

          _initializerDefineProperty(this, "triggers", _descriptor2, this);

          this.sprite = null;
          // 标记是否修改过
          this._isDirty = false;
          this._waveMap = new Map();
          this._wavePreviews = [];
          this._isPlaying = false;
          this._isActive = false;
          this._positionBeforePlay = new Vec3();
        }

        onLoad() {
          this.sprite = this.node.getComponent(Sprite) || this.node.addComponent(Sprite);
          var eventSpriteUUID = 'aa8d57da-85f6-4319-8b4d-2a7864dccbe7';
          assetManager.loadAny(eventSpriteUUID, (err, asset) => {
            if (err) {
              console.warn(err);
              return;
            }

            this.sprite.spriteFrame = SpriteFrame.createWithImage(asset);
            var uiTransform = this.node.getComponent(UITransform) || this.node.addComponent(UITransform);
            uiTransform.setContentSize(256, 256);
          });
        }

        onFocusInEditor() {
          this._isDirty = true;
        }

        update(dt) {
          for (var i = 0; i < this.conditions.length; i++) {
            var cond = this.conditions[i];
            cond._index = i;

            if (cond.type == (_crd && LevelDataEventCondtionType === void 0 ? (_reportPossibleCrUseOfLevelDataEventCondtionType({
              error: Error()
            }), LevelDataEventCondtionType) : LevelDataEventCondtionType).Wave && cond.data.targetElemID != "" && cond._targetElem == null) {
              var elems = this.node.scene.getComponentsInChildren(_crd && LevelEditorElemUI === void 0 ? (_reportPossibleCrUseOfLevelEditorElemUI({
                error: Error()
              }), LevelEditorElemUI) : LevelEditorElemUI);

              for (var elem of elems) {
                if (elem.elemID == cond.data.targetElemID) {
                  cond._targetElem = elem;
                  break;
                }
              }
            }
          }

          if (this._isDirty) {
            this.syncWaveOffset(); // clear wave childrens 

            this.node.removeAllChildren();

            this._waveMap.clear(); // 重新创建波次预览节点


            this.triggers.forEach(trigger => {
              trigger.waveGroup.forEach(waveGroup => {
                if (waveGroup.wave.wavePrefab) {
                  var prefab = waveGroup.wave.wavePrefab;
                  var waveNode = instantiate(prefab);
                  this.node.addChild(waveNode);
                  waveNode.setPosition(waveGroup.wave.waveOffset.x, waveGroup.wave.waveOffset.y);
                  this.setupWave(waveNode.getComponent(_crd && Wave === void 0 ? (_reportPossibleCrUseOfWave({
                    error: Error()
                  }), Wave) : Wave));

                  this._waveMap.set(prefab.uuid, waveNode.getComponent(_crd && Wave === void 0 ? (_reportPossibleCrUseOfWave({
                    error: Error()
                  }), Wave) : Wave));
                }
              });
            });
            this._isDirty = false;
          }
        }

        syncWaveOffset() {
          if (this._waveMap.size > 0) {
            // 将位置修改同步回来
            this._waveMap.forEach((wave, key) => {
              if (!wave || !wave.node) return;
              var waveOffset = wave.node.position;

              for (var trigger of this.triggers) {
                trigger.setWaveOffset(key, new Vec2(waveOffset.x, waveOffset.y));
              }
            });
          }
        }

        initByLevelData(data) {
          var _this = this;

          super.initByLevelData(data);

          if (data.conditions) {
            for (var i = 0; i < data.conditions.length; i++) {
              var condition = new (_crd && LevelEditorCondition === void 0 ? (_reportPossibleCrUseOfLevelEditorCondition({
                error: Error()
              }), LevelEditorCondition) : LevelEditorCondition)();
              condition._index = i;
              condition.data = data.conditions[i];
              this.conditions.push(condition);
            }
          }

          if (data.triggers) {
            var _loop = function _loop() {
              var trigger = new LevelEditorEventTrigger();
              trigger._index = _i;
              trigger.data = data.triggers[_i];

              if (trigger.data._type == (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
                error: Error()
              }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Audio) {
                var uuid = trigger.data.audioUUID;

                if (uuid != "") {
                  assetManager.loadAny({
                    uuid: uuid
                  }, (err, audio) => {
                    if (err) {
                      console.error("LevelEditorEventUI initByLevelData load audio err", err);
                      return;
                    }

                    trigger._audio = audio;
                  });
                }
              }

              if (trigger.data._type == (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
                error: Error()
              }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Wave) {
                var waveTrigger = trigger.data;

                var _loop2 = function _loop2() {
                  var editorWaveGroup = new (_crd && LevelEditorWaveGroup === void 0 ? (_reportPossibleCrUseOfLevelEditorWaveGroup({
                    error: Error()
                  }), LevelEditorWaveGroup) : LevelEditorWaveGroup)(); // console.log('waveGroup.wave.waveUUID: ', waveGroup);

                  assetManager.loadAny({
                    uuid: waveGroup.wave.waveUUID
                  }, (err, prefab) => {
                    if (err) {
                      console.error("LevelEditorEventUI initByLevelData load wave prefab err", err);
                      return;
                    }

                    editorWaveGroup.wave.wavePrefab = prefab;
                  });
                  editorWaveGroup.wave.waveOffset = waveGroup.wave.waveOffset;
                  editorWaveGroup.weight = waveGroup.weight;
                  trigger.waveGroup.push(editorWaveGroup);
                };

                for (var waveGroup of waveTrigger.waveGroup) {
                  _loop2();
                }
              }

              _this.triggers.push(trigger);
            };

            for (var _i = 0; _i < data.triggers.length; _i++) {
              _loop();
            }
          }

          this._isDirty = true;
        }

        fillLevelData(data) {
          super.fillLevelData(data);
          data.conditions = [];
          this.conditions.forEach(cond => {
            if (cond != null) {
              data.conditions.push(cond.data);
            }
          });
          this.syncWaveOffset();
          this.triggers.forEach(trigger => {
            if (trigger != null) {
              data.triggers.push(trigger.data);
            }
          });
        }

        copyFrom(source) {
          this.elemID = source.elemID;
          this.remark = source.remark;
          this.node.position = source.node.position;
          this.conditions = source.conditions.map(cond => {
            var newCond = new (_crd && LevelEditorCondition === void 0 ? (_reportPossibleCrUseOfLevelEditorCondition({
              error: Error()
            }), LevelEditorCondition) : LevelEditorCondition)();
            newCond.copyFrom(cond);
            return newCond;
          });
          ;
          this.triggers = source.triggers.map(trigger => {
            var newTrigger = new LevelEditorEventTrigger();
            newTrigger.copyFrom(trigger);
            return newTrigger;
          });
          ;
        }

        setupWave(wave) {
          if (wave == null) return;
          wave.setupInEditor();
        }

        play(bPlay, progress) {
          if (bPlay != this._isPlaying) {
            this._isPlaying = bPlay;

            if (bPlay) {
              this.node.getPosition(this._positionBeforePlay);
            } else {
              this.node.setPosition(this._positionBeforePlay);
            }
          }

          if (bPlay) {
            var posY = this._positionBeforePlay.y - progress;

            if (posY <= (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
              error: Error()
            }), GameConst) : GameConst).VIEWPORT_TOP) {
              if (!this._isActive) {
                this.triggers.forEach(trigger => {
                  this.performTrigger(trigger.data, posY);
                });
                this._isActive = true;
              }
            }

            this.node.setPosition(this._positionBeforePlay.x, posY, this._positionBeforePlay.z);
          } else {
            if (this._isActive) {
              this.resetPlay();
              this._isActive = false;
            }
          }
        }

        resetPlay() {
          this._wavePreviews.forEach(wavePreview => {
            wavePreview.clearPreview();
          });

          this._wavePreviews = [];
        }

        performTrigger(trigger, offsetY) {
          switch (trigger._type) {
            case (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
              error: Error()
            }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Log:
              console.log("LevelEditorEventUI", "trigger log", trigger.message);
              break;

            case (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
              error: Error()
            }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Audio:
              break;

            case (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
              error: Error()
            }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Wave:
              // Do Wave logic
              this.triggerWave(trigger, offsetY);
              break;

            default:
              break;
          }
        }

        triggerWave(triggerWave, offsetY) {
          console.log('Trigger wave!! , ', offsetY);
          var randomWeight = Math.floor(Math.random() * 100);
          var curWeight = 0;
          var selectedWaveGroup = null;

          for (var waveGroup of triggerWave.waveGroup) {
            curWeight += waveGroup.weight;

            if (randomWeight <= curWeight) {
              selectedWaveGroup = waveGroup;
              break;
            }
          }

          if (selectedWaveGroup == null) {
            return;
          }

          var wave = this._waveMap.get(selectedWaveGroup.wave.waveUUID);

          if (wave) {
            var position = this.node.position;
            var wavePreview = wave.node.getComponentInChildren(_crd && WavePreview === void 0 ? (_reportPossibleCrUseOfWavePreview({
              error: Error()
            }), WavePreview) : WavePreview);

            if (wavePreview) {
              wavePreview.triggerPreview(position.x, offsetY);

              this._wavePreviews.push(wavePreview);
            }
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class5.prototype, "conditions", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class5.prototype, "triggers", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      })), _class5)) || _class4) || _class4));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=6a0c1df255fa2bfb0e32d0364e23dcfc018d0664.js.map