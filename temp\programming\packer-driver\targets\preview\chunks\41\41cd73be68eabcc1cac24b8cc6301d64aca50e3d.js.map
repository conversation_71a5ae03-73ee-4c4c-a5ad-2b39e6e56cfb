{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/preview/WavePreview.ts"], "names": ["_decorator", "Graphics", "Color", "Component", "instantiate", "assetManager", "CCObject", "Wave", "LubanMgr", "EnemyPlane", "eMoveEvent", "PathData", "PathEditor", "ccclass", "property", "executeInEditMode", "menu", "requireComponent", "WavePreview", "_luban", "_wave", "_graphics", "isPreviewing", "planePool", "activePlane", "luban", "onLoad", "node", "parent", "getComponent", "addComponent", "reset", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "update", "dt", "tickPreviewDraw", "tickPreview", "pathAsset", "waveData", "path", "fromJSON", "json", "subdivided", "getSubdividedPoints", "isWaveActive", "isSpawnCompleted", "waveElapsedTime", "color", "GREEN", "RED", "length", "drawUniformPath", "closed", "endPoint", "prevPoint", "direction", "Math", "atan2", "y", "x", "drawPathDirectionArrow", "position", "drawPathPoints", "points", "i", "point", "drawPathPointAtPosition", "startIdx", "endIdx", "triggerPreview", "posX", "posY", "setCreatePlaneDelegate", "planeId", "pos", "angle", "createDummyPlane", "trigger", "dtInMiliseconds", "tick", "for<PERSON>ach", "plane", "moveCom", "clearPreview", "destroy", "wave", "pop", "active", "dummy_plane_uuid", "loadAny", "uuid", "err", "prefab", "console", "error", "canvas", "scene", "getChildByName", "planeNode", "hideFlags", "Flags", "AllHideMasks", "<PERSON><PERSON><PERSON><PERSON>", "initMove", "removeAllListeners", "on", "onBecomeInvisible", "push", "initPath"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACSA,MAAAA,U,OAAAA,U;AAA0BC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,Y,OAAAA,Y;AAAcC,MAAAA,Q,OAAAA,Q;;AAGvFC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,Q,iBAAAA,Q;;AAEFC,MAAAA,U;;AAGEC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;;;OAVH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA,iBAArB;AAAwCC,QAAAA,IAAxC;AAA8CC,QAAAA;AAA9C,O,GAAmEjB,U;;AAYzE;6BAKakB,W,WAJZL,OAAO,CAAC,aAAD,C,UACPG,IAAI,CAAC,aAAD,C,UACJC,gBAAgB,CAAChB,QAAD,C,UAChBc,iBAAiB,E,6DAHlB,MAIaG,WAJb,SAIiCf,SAJjC,CAI2C;AAAA;AAAA;AAAA,eAC/BgB,MAD+B,GACP,IADO;AAAA,eAS/BC,KAT+B,GASZ,IATY;AAAA,eAU/BC,SAV+B,GAUJ,IAVI;AAuEvC;AAvEuC,eAwE/BC,YAxE+B,GAwEP,KAxEO;AAAA,eAyE/BC,SAzE+B,GAyEL,EAzEK;AAAA,eA0E/BC,WA1E+B,GA0EH,EA1EG;AAAA;;AAEvB,YAALC,KAAK,GAAkB;AAC9B,cAAI,KAAKN,MAAL,IAAe,IAAnB,EAAyB;AACrB,iBAAKA,MAAL,GAAc;AAAA;AAAA,uCAAd;AACH;;AACD,iBAAO,KAAKA,MAAZ;AACH;;AAIDO,QAAAA,MAAM,GAAG;AACL,eAAKN,KAAL,GAAa,KAAKO,IAAL,CAAUC,MAAV,CAAkBC,YAAlB;AAAA;AAAA,2BAAb;AACA,eAAKR,SAAL,GAAiB,KAAKQ,YAAL,CAAkB5B,QAAlB,KAA+B,KAAK6B,YAAL,CAAkB7B,QAAlB,CAAhD;AACH;;AAED8B,QAAAA,KAAK,GAAG;AACJ,eAAKJ,IAAL,CAAUK,iBAAV;AACH;;AAEDC,QAAAA,MAAM,CAACC,EAAD,EAAa;AACf,eAAKC,eAAL;AACA,eAAKC,WAAL,CAAiBF,EAAjB;AACH;;AAEOC,QAAAA,eAAe,GAAG;AAAA;;AACtB,cAAME,SAAS,kBAAG,KAAKjB,KAAR,qBAAG,YAAYkB,QAAZ,CAAqBD,SAAvC;;AACA,cAAIA,SAAJ,EAAe;AACX;AACA,gBAAME,IAAI,GAAG;AAAA;AAAA,sCAASC,QAAT,CAAkBH,SAAS,CAACI,IAA5B,CAAb;AACA,gBAAMC,UAAU,GAAGH,IAAI,CAACI,mBAAL,EAAnB;AAEA,gBAAMC,YAAY,GAAG,KAAKtB,YAAL,IAAqB,CAAC,KAAKF,KAAL,CAAYyB,gBAAlC,IAAsD,KAAKzB,KAAL,CAAY0B,eAAZ,GAA8B,CAAzG;AACA,gBAAMC,KAAK,GAAGH,YAAY,GAAG1C,KAAK,CAAC8C,KAAT,GAAiB9C,KAAK,CAAC+C,GAAjD;;AACA,gBAAIP,UAAU,CAACQ,MAAX,GAAoB,CAAxB,EAA2B;AACvB;AAAA;AAAA,4CAAWC,eAAX,CAA2B,KAAK9B,SAAhC,EAA4CqB,UAA5C,EAAwDK,KAAxD,EAA+DR,IAAI,CAACa,MAApE,EAA4E,EAA5E;AACA,kBAAMC,QAAQ,GAAGX,UAAU,CAACA,UAAU,CAACQ,MAAX,GAAoB,CAArB,CAA3B;AACA,kBAAII,SAAS,GAAGZ,UAAU,CAACA,UAAU,CAACQ,MAAX,GAAoB,CAArB,CAA1B;;AACA,kBAAIR,UAAU,CAACQ,MAAX,IAAqB,CAAzB,EAA4B;AACxBI,gBAAAA,SAAS,GAAGZ,UAAU,CAACA,UAAU,CAACQ,MAAX,GAAoB,CAArB,CAAtB;AACH;;AACD,kBAAMK,SAAS,GAAGC,IAAI,CAACC,KAAL,CAAWJ,QAAQ,CAACK,CAAT,GAAaJ,SAAS,CAACI,CAAlC,EAAqCL,QAAQ,CAACM,CAAT,GAAaL,SAAS,CAACK,CAA5D,CAAlB;AACA;AAAA;AAAA,4CAAWC,sBAAX,CAAkC,KAAKvC,SAAvC,EAAmDgC,QAAQ,CAACQ,QAA5D,EAAsEN,SAAtE,EAAiFhB,IAAI,CAACa,MAAtF;AACH,aAhBU,CAiBX;;;AACA,iBAAKU,cAAL,CAAoBvB,IAApB;AACH;AACJ;;AAEOuB,QAAAA,cAAc,CAACvB,IAAD,EAAiB;AACnC,cAAI,CAACA,IAAI,CAACwB,MAAN,IAAgBxB,IAAI,CAACwB,MAAL,CAAYb,MAAZ,KAAuB,CAA3C,EAA8C,OADX,CAGnC;;AACA,eAAK,IAAIc,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGzB,IAAI,CAACwB,MAAL,CAAYb,MAAhC,EAAwCc,CAAC,EAAzC,EAA6C;AACzC,gBAAMC,KAAK,GAAG1B,IAAI,CAACwB,MAAL,CAAYC,CAAZ,CAAd;AAEA;AAAA;AAAA,0CAAWE,uBAAX,CACI,KAAK7C,SADT,EAEI4C,KAFJ,EAGIA,KAAK,CAACN,CAHV,EAIIM,KAAK,CAACP,CAJV,EAKI,KALJ,EAKW;AACP,cANJ,EAMW;AACPM,YAAAA,CAPJ,EAQIzB,IAAI,CAACwB,MAAL,CAAYb,MARhB,EASIX,IAAI,CAAC4B,QATT,EAUI5B,IAAI,CAAC6B,MAVT;AAYH;AACJ;;AAODC,QAAAA,cAAc,CAACC,IAAD,EAAeC,IAAf,EAA6B;AAAA;;AACvC;AAEA,cAAI,CAAC,KAAKnD,KAAV,EAAiB;AACb;AACH;;AAED,eAAKA,KAAL,CAAWoD,sBAAX,iCAAkC,WAAOC,OAAP,EAAwBC,GAAxB,EAAmCC,KAAnC,EAAqD;AACnF,YAAA,KAAI,CAACC,gBAAL,CAAsB,KAAI,CAACxD,KAA3B,EAAmCqD,OAAnC,EAA4CC,GAA5C,EAAiDC,KAAjD;AACH,WAFD;;AAGA,eAAKvD,KAAL,CAAWyD,OAAX,CAAmBP,IAAnB,EAAyBC,IAAzB;;AACA,eAAKjD,YAAL,GAAoB,IAApB;AACH;;AAEDc,QAAAA,WAAW,CAACF,EAAD,EAAa;AACpB,cAAI,CAAC,KAAKZ,YAAV,EAAwB;AACpB;AACH;;AAED,cAAMwD,eAAe,GAAG5C,EAAE,GAAG,IAA7B;;AACA,cAAI,KAAKd,KAAT,EAAgB;AACZ,iBAAKA,KAAL,CAAW2D,IAAX,CAAgBD,eAAhB;AACH;;AAED,eAAKtD,WAAL,CAAiBwD,OAAjB,CAA0BC,KAAD,IAAW;AAChCA,YAAAA,KAAK,CAACC,OAAN,CAAeH,IAAf,CAAoB7C,EAApB;AACH,WAFD;AAGH;;AAEDiD,QAAAA,YAAY,GAAG;AACX;AACA,eAAK7D,YAAL,GAAoB,KAApB,CAFW,CAGX;;AACA,eAAKE,WAAL,CAAiBwD,OAAjB,CAA0BC,KAAD,IAAW;AAChCA,YAAAA,KAAK,CAACtD,IAAN,CAAWyD,OAAX;AACH,WAFD;AAGA,eAAK7D,SAAL,CAAeyD,OAAf,CAAwBC,KAAD,IAAW;AAC9BA,YAAAA,KAAK,CAACtD,IAAN,CAAWyD,OAAX;AACH,WAFD,EAPW,CAUX;;AACA,eAAKzD,IAAL,CAAUK,iBAAV;AAEA,eAAKR,WAAL,GAAmB,EAAnB;AACA,eAAKD,SAAL,GAAiB,EAAjB;AACH;;AAEOqD,QAAAA,gBAAgB,CAACS,IAAD,EAAaZ,OAAb,EAA8BC,GAA9B,EAAyCC,KAAzC,EAAwD;AAAA;;AAC5E;AACA;AACA,cAAIM,KAAsB,GAAG,IAA7B;;AACA,cAAI,KAAK1D,SAAL,CAAe2B,MAAf,GAAwB,CAA5B,EAA+B;AAC3B;AACA+B,YAAAA,KAAK,GAAG,KAAK1D,SAAL,CAAe+D,GAAf,EAAR;AACAL,YAAAA,KAAK,CAACtD,IAAN,CAAW4D,MAAX,GAAoB,IAApB;AACH,WAJD,MAKK;AACD,gBAAMC,gBAAwB,GAAG,sCAAjC;AACAnF,YAAAA,YAAY,CAACoF,OAAb,CAAqB;AAACC,cAAAA,IAAI,EAACF;AAAN,aAArB,iCAA8C,WAAOG,GAAP,EAAYC,MAAZ,EAA8B;AACxE,kBAAID,GAAJ,EAAS;AACLE,gBAAAA,OAAO,CAACC,KAAR,CAAc,8CAAd,EAA8DH,GAA9D;AACA;AACH;;AACD,kBAAI;AACA;AACA;AACA;AACA,oBAAMI,MAAM,GAAG,MAAI,CAACpE,IAAL,CAAUqE,KAAV,CAAgBC,cAAhB,CAA+B,QAA/B,CAAf;;AACA,oBAAI,CAACF,MAAL,EAAa;AACTF,kBAAAA,OAAO,CAACC,KAAR,CAAc,wCAAd;AACA;AACH;;AACD,oBAAMI,SAAS,GAAG9F,WAAW,CAACwF,MAAD,CAA7B;AACAM,gBAAAA,SAAS,CAACC,SAAV,GAAsB7F,QAAQ,CAAC8F,KAAT,CAAeC,YAArC;;AACA,oBAAMpB,MAAK,GAAGiB,SAAS,CAAErE,YAAX;AAAA;AAAA,6CAAd;;AACA,oBAAIoD,MAAJ,EAAW;AACPc,kBAAAA,MAAM,CAACO,QAAP,CAAgBJ,SAAhB,EADO,CAEP;AACA;AACA;AACA;;AACAjB,kBAAAA,MAAK,CAACsB,QAAN,CAAe7B,GAAG,CAACf,CAAnB,EAAsBe,GAAG,CAAChB,CAA1B,EAA6BiB,KAA7B;;AACAM,kBAAAA,MAAK,CAACC,OAAN,CAAesB,kBAAf;;AACAvB,kBAAAA,MAAK,CAACC,OAAN,CAAeuB,EAAf,CAAkB;AAAA;AAAA,gDAAWC,iBAA7B,EAAgD,MAAM;AAClDzB,oBAAAA,MAAK,CAACtD,IAAN,CAAW4D,MAAX,GAAoB,KAApB;;AACA,oBAAA,MAAI,CAAChE,SAAL,CAAeoF,IAAf,CAAoB1B,MAApB;AACH,mBAHD;;AAIA,sBAAII,IAAI,CAAC9C,IAAT,EAAe;AACX0C,oBAAAA,MAAK,CAAC2B,QAAN,CAAelC,GAAG,CAACf,CAAnB,EAAsBe,GAAG,CAAChB,CAA1B,EAA6B2B,IAAI,CAAC9C,IAAlC;AACH;;AACD,kBAAA,MAAI,CAACf,WAAL,CAAiBmF,IAAjB,CAAsB1B,MAAtB;AACH,iBAhBD,MAgBO;AACHiB,kBAAAA,SAAS,CAACd,OAAV;AACH;AACJ,eA/BD,CAgCA,OAAOU,KAAP,EAAc;AACVD,gBAAAA,OAAO,CAACC,KAAR,CAAc,kCAAd,EAAkDA,KAAlD;AACH;AACJ,aAxCD;AAyCH;AACJ;;AA/KsC,O", "sourcesContent": ["\r\nimport { _decorator, Node, Prefab, Graphics, Color, Component, Vec2, instantiate, assetManager, CCObject } from 'cc';\r\nconst { ccclass, property, executeInEditMode, menu, requireComponent } = _decorator;\r\nimport { WaveData, eSpawnOrder, eWaveCompletion } from 'db://assets/bundles/common/script/game/data/WaveData';\r\nimport { Wave } from 'db://assets/bundles/common/script/game/wave/Wave';\r\nimport { LubanMgr } from 'db://assets/bundles/common/script/luban/LubanMgr';\r\nimport { ObjectPool } from 'db://assets/bundles/common/script/game/bullet/ObjectPool';\r\nimport EnemyPlane from 'db://assets/bundles/common/script/game/ui/plane/enemy/EnemyPlane';\r\nimport { LevelEditorUtils } from '../utils';\r\nimport { EnemyData } from 'db://assets/bundles/common/script/game/data/EnemyData';\r\nimport { eMoveEvent } from 'db://assets/bundles/common/script/game/move/IMovable';\r\nimport { PathData } from 'db://assets/bundles/common/script/game/data/PathData';\r\nimport { PathEditor } from 'db://assets/editor/level/wave/PathEditor';\r\n\r\n/// 用来创建和管理波次的所有飞机对象\r\n@ccclass('WavePreview')\r\n@menu(\"怪物/编辑器/波次预览\")\r\n@requireComponent(Graphics)\r\n@executeInEditMode()\r\nexport class WavePreview extends Component {\r\n    private _luban: LubanMgr|null = null;\r\n    public get luban(): LubanMgr|null {\r\n        if (this._luban == null) {\r\n            this._luban = new LubanMgr();\r\n        }\r\n        return this._luban;\r\n    }\r\n\r\n    private _wave: Wave|null = null;\r\n    private _graphics: Graphics|null = null;\r\n    onLoad() {\r\n        this._wave = this.node.parent!.getComponent(Wave);\r\n        this._graphics = this.getComponent(Graphics) || this.addComponent(Graphics);\r\n    }\r\n\r\n    reset() {\r\n        this.node.removeAllChildren();\r\n    }\r\n\r\n    update(dt: number) {\r\n        this.tickPreviewDraw();\r\n        this.tickPreview(dt);\r\n    }\r\n\r\n    private tickPreviewDraw() {\r\n        const pathAsset = this._wave?.waveData.pathAsset;\r\n        if (pathAsset) {\r\n            // draw path\r\n            const path = PathData.fromJSON(pathAsset.json);\r\n            const subdivided = path.getSubdividedPoints();\r\n\r\n            const isWaveActive = this.isPreviewing && !this._wave!.isSpawnCompleted && this._wave!.waveElapsedTime > 0;\r\n            const color = isWaveActive ? Color.GREEN : Color.RED;\r\n            if (subdivided.length > 1) {\r\n                PathEditor.drawUniformPath(this._graphics!, subdivided, color, path.closed, 10);\r\n                const endPoint = subdivided[subdivided.length - 1];\r\n                let prevPoint = subdivided[subdivided.length - 2];\r\n                if (subdivided.length >= 5) {\r\n                    prevPoint = subdivided[subdivided.length - 5];\r\n                }\r\n                const direction = Math.atan2(endPoint.y - prevPoint.y, endPoint.x - prevPoint.x);\r\n                PathEditor.drawPathDirectionArrow(this._graphics!, endPoint.position, direction, path.closed);\r\n            }\r\n            // draw path points\r\n            this.drawPathPoints(path);\r\n        }\r\n    }\r\n\r\n    private drawPathPoints(path: PathData) {\r\n        if (!path.points || path.points.length === 0) return;\r\n\r\n        // 使用同一个 Graphics 绘制所有路径点\r\n        for (let i = 0; i < path.points.length; i++) {\r\n            const point = path.points[i];\r\n\r\n            PathEditor.drawPathPointAtPosition(\r\n                this._graphics!,\r\n                point,\r\n                point.x,\r\n                point.y,\r\n                false, // 不选中状态\r\n                15,    // 点大小稍小一些\r\n                i,\r\n                path.points.length,\r\n                path.startIdx,\r\n                path.endIdx\r\n            );\r\n        }\r\n    }\r\n\r\n    // 这里的wave时编辑器play时，用来动态创建小怪的wave。\r\n    private isPreviewing: boolean = false;\r\n    private planePool: EnemyPlane[] = []\r\n    private activePlane: EnemyPlane[] = [];\r\n\r\n    triggerPreview(posX: number, posY: number) {\r\n        // console.log('WavePreview - triggerPreview: ', posX, posY, this._wave);\r\n\r\n        if (!this._wave) {\r\n            return;\r\n        }\r\n\r\n        this._wave.setCreatePlaneDelegate(async (planeId: number, pos: Vec2, angle: number) => {\r\n            this.createDummyPlane(this._wave!, planeId, pos, angle);\r\n        });\r\n        this._wave.trigger(posX, posY);\r\n        this.isPreviewing = true;\r\n    }\r\n\r\n    tickPreview(dt: number) {\r\n        if (!this.isPreviewing) {\r\n            return;\r\n        }\r\n\r\n        const dtInMiliseconds = dt * 1000;\r\n        if (this._wave) {\r\n            this._wave.tick(dtInMiliseconds);\r\n        }\r\n\r\n        this.activePlane.forEach((plane) => {\r\n            plane.moveCom!.tick(dt);\r\n        });\r\n    }\r\n\r\n    clearPreview() {\r\n        // console.log('WavePreview - clearPreview: ', this.activePlane.length);\r\n        this.isPreviewing = false;\r\n        // destroy preivewWave\r\n        this.activePlane.forEach((plane) => {\r\n            plane.node.destroy();\r\n        });\r\n        this.planePool.forEach((plane) => {\r\n            plane.node.destroy();\r\n        });\r\n        // just in case\r\n        this.node.removeAllChildren();\r\n\r\n        this.activePlane = [];\r\n        this.planePool = [];\r\n    }\r\n\r\n    private createDummyPlane(wave: Wave, planeId: number, pos: Vec2, angle: number) {\r\n        // 对应\"assets/editor/level/prefab/dummy_plane\";\r\n        // console.log('WavePreview - createDummyPlane: ', planeId, pos, angle);\r\n        let plane: EnemyPlane|null = null;\r\n        if (this.planePool.length > 0) {\r\n            // 从对象池里拿一个dummy plane\r\n            plane = this.planePool.pop()!;\r\n            plane.node.active = true;\r\n        }\r\n        else {\r\n            const dummy_plane_uuid: string = \"698c56c6-6603-4e69-abaf-421b721ef307\";\r\n            assetManager.loadAny({uuid:dummy_plane_uuid}, async (err, prefab:Prefab) => {\r\n                if (err) {\r\n                    console.error(\"WavePreview createDummyPlane load prefab err\", err);\r\n                    return;\r\n                }\r\n                try {\r\n                    // if (this.luban?.table == null) {\r\n                    //     await this.luban?.initInEditor();\r\n                    // }\r\n                    const canvas = this.node.scene.getChildByName(\"Canvas\")!;\r\n                    if (!canvas) {\r\n                        console.error(\"WavePreview createDummyPlane no canvas\");\r\n                        return;\r\n                    }\r\n                    const planeNode = instantiate(prefab);\r\n                    planeNode.hideFlags = CCObject.Flags.AllHideMasks;\r\n                    const plane = planeNode!.getComponent(EnemyPlane);\r\n                    if (plane) {\r\n                        canvas.addChild(planeNode);\r\n                        // this.node.parent!.addChild(planeNode);\r\n                        // const enemyData = new EnemyData(planeId, this.luban?.table.TbResEnemy.get(planeId));\r\n                        // const prefab = await LevelEditorUtils.loadByPath<Prefab>(enemyData.recoursePrefab);\r\n                        // plane.initPlane(enemyData, prefab!);\r\n                        plane.initMove(pos.x, pos.y, angle);\r\n                        plane.moveCom!.removeAllListeners();\r\n                        plane.moveCom!.on(eMoveEvent.onBecomeInvisible, () => {\r\n                            plane.node.active = false;\r\n                            this.planePool.push(plane);\r\n                        });\r\n                        if (wave.path) {\r\n                            plane.initPath(pos.x, pos.y, wave.path);\r\n                        } \r\n                        this.activePlane.push(plane);\r\n                    } else {\r\n                        planeNode.destroy();\r\n                    }\r\n                }\r\n                catch (error) {\r\n                    console.error(\"WavePreview createDummyPlane err\", error);\r\n                }\r\n            });\r\n        }\r\n    }\r\n}"]}