
import { _decorator, Node, Prefab, Graphics, Color, Component, Vec2, instantiate, assetManager, CCObject } from 'cc';
const { ccclass, property, executeInEditMode, menu, requireComponent } = _decorator;
import { WaveData, eSpawnOrder, eWaveCompletion } from 'db://assets/bundles/common/script/game/data/WaveData';
import { Wave } from 'db://assets/bundles/common/script/game/wave/Wave';
import { LubanMgr } from 'db://assets/bundles/common/script/luban/LubanMgr';
import { ObjectPool } from 'db://assets/bundles/common/script/game/bullet/ObjectPool';
import EnemyPlane from 'db://assets/bundles/common/script/game/ui/plane/enemy/EnemyPlane';
import { LevelEditorUtils } from '../utils';
import { EnemyData } from 'db://assets/bundles/common/script/game/data/EnemyData';
import { eMoveEvent } from 'db://assets/bundles/common/script/game/move/IMovable';
import { PathData } from 'db://assets/bundles/common/script/game/data/PathData';
import { PathEditor } from 'db://assets/editor/level/wave/PathEditor';

/// 用来创建和管理波次的所有飞机对象
@ccclass('WavePreview')
@menu("怪物/编辑器/波次预览")
@requireComponent(Graphics)
@executeInEditMode()
export class WavePreview extends Component {
    private _luban: LubanMgr|null = null;
    public get luban(): LubanMgr|null {
        if (this._luban == null) {
            this._luban = new LubanMgr();
        }
        return this._luban;
    }

    private _wave: Wave|null = null;
    private _graphics: Graphics|null = null;
    onLoad() {
        this._wave = this.node.parent!.getComponent(Wave);
        this._graphics = this.getComponent(Graphics) || this.addComponent(Graphics);
    }

    reset() {
        this.node.removeAllChildren();
    }

    update(dt: number) {
        this.tickPreviewDraw();
        this.tickPreview(dt);
    }

    private tickPreviewDraw() {
        const pathAsset = this._wave?.waveData.pathAsset;
        if (pathAsset) {
            // draw path
            const path = PathData.fromJSON(pathAsset.json);
            const subdivided = path.getSubdividedPoints();

            const isWaveActive = this.isPreviewing && !this._wave!.isSpawnCompleted && this._wave!.waveElapsedTime > 0;
            const color = isWaveActive ? Color.GREEN : Color.RED;
            if (subdivided.length > 1) {
                PathEditor.drawUniformPath(this._graphics!, subdivided, color, path.closed, 10);
                const endPoint = subdivided[subdivided.length - 1];
                let prevPoint = subdivided[subdivided.length - 2];
                if (subdivided.length >= 5) {
                    prevPoint = subdivided[subdivided.length - 5];
                }
                const direction = Math.atan2(endPoint.y - prevPoint.y, endPoint.x - prevPoint.x);
                PathEditor.drawPathDirectionArrow(this._graphics!, endPoint.position, direction, path.closed);
            }
            // draw path points
            this.drawPathPoints(path);
        }
    }

    private drawPathPoints(path: PathData) {
        if (!path.points || path.points.length === 0) return;

        // 使用同一个 Graphics 绘制所有路径点
        for (let i = 0; i < path.points.length; i++) {
            const point = path.points[i];

            PathEditor.drawPathPointAtPosition(
                this._graphics!,
                point,
                point.x,
                point.y,
                false, // 不选中状态
                15,    // 点大小稍小一些
                i,
                path.points.length,
                path.startIdx,
                path.endIdx
            );
        }
    }

    // 这里的wave时编辑器play时，用来动态创建小怪的wave。
    private isPreviewing: boolean = false;
    private planePool: EnemyPlane[] = []
    private activePlane: EnemyPlane[] = [];

    triggerPreview(posX: number, posY: number) {
        // console.log('WavePreview - triggerPreview: ', posX, posY, this._wave);

        if (!this._wave) {
            return;
        }

        this._wave.setCreatePlaneDelegate(async (planeId: number, pos: Vec2, angle: number) => {
            this.createDummyPlane(this._wave!, planeId, pos, angle);
        });
        this._wave.trigger(posX, posY);
        this.isPreviewing = true;
    }

    tickPreview(dt: number) {
        if (!this.isPreviewing) {
            return;
        }

        const dtInMiliseconds = dt * 1000;
        if (this._wave) {
            this._wave.tick(dtInMiliseconds);
        }

        this.activePlane.forEach((plane) => {
            plane.moveCom!.tick(dt);
        });
    }

    clearPreview() {
        // console.log('WavePreview - clearPreview: ', this.activePlane.length);
        this.isPreviewing = false;
        // destroy preivewWave
        this.activePlane.forEach((plane) => {
            plane.node.destroy();
        });
        this.planePool.forEach((plane) => {
            plane.node.destroy();
        });
        // just in case
        this.node.removeAllChildren();

        this.activePlane = [];
        this.planePool = [];
    }

    private createDummyPlane(wave: Wave, planeId: number, pos: Vec2, angle: number) {
        // 对应"assets/editor/level/prefab/dummy_plane";
        // console.log('WavePreview - createDummyPlane: ', planeId, pos, angle);
        let plane: EnemyPlane|null = null;
        if (this.planePool.length > 0) {
            // 从对象池里拿一个dummy plane
            plane = this.planePool.pop()!;
            plane.node.active = true;
        }
        else {
            const dummy_plane_uuid: string = "698c56c6-6603-4e69-abaf-421b721ef307";
            assetManager.loadAny({uuid:dummy_plane_uuid}, async (err, prefab:Prefab) => {
                if (err) {
                    console.error("WavePreview createDummyPlane load prefab err", err);
                    return;
                }
                try {
                    // if (this.luban?.table == null) {
                    //     await this.luban?.initInEditor();
                    // }
                    const canvas = this.node.scene.getChildByName("Canvas")!;
                    if (!canvas) {
                        console.error("WavePreview createDummyPlane no canvas");
                        return;
                    }
                    const planeNode = instantiate(prefab);
                    planeNode.hideFlags = CCObject.Flags.AllHideMasks;
                    const plane = planeNode!.getComponent(EnemyPlane);
                    if (plane) {
                        canvas.addChild(planeNode);
                        // this.node.parent!.addChild(planeNode);
                        // const enemyData = new EnemyData(planeId, this.luban?.table.TbResEnemy.get(planeId));
                        // const prefab = await LevelEditorUtils.loadByPath<Prefab>(enemyData.recoursePrefab);
                        // plane.initPlane(enemyData, prefab!);
                        plane.initMove(pos.x, pos.y, angle);
                        plane.moveCom!.removeAllListeners();
                        plane.moveCom!.on(eMoveEvent.onBecomeInvisible, () => {
                            plane.node.active = false;
                            this.planePool.push(plane);
                        });
                        if (wave.path) {
                            plane.initPath(pos.x, pos.y, wave.path);
                        } 
                        this.activePlane.push(plane);
                    } else {
                        planeNode.destroy();
                    }
                }
                catch (error) {
                    console.error("WavePreview createDummyPlane err", error);
                }
            });
        }
    }
}